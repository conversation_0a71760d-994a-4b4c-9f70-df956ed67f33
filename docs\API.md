# Russian Flashcards API Documentation

Base URL: `http://localhost:3000/api`

## Authentication

All protected endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <token>
```

## Response Format

All API responses follow this format:
```json
{
  "message": "Success message",
  "data": { ... },
  "error": "Error message (if applicable)"
}
```

## Authentication Endpoints

### Register User
**POST** `/auth/register`

Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Response (201):**
```json
{
  "message": "User registered successfully",
  "token": "jwt_token_here",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Login User
**POST** `/auth/login`

Authenticate user and get access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200):**
```json
{
  "message": "Login successful",
  "token": "jwt_token_here",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  }
}
```

### Verify Token
**GET** `/auth/verify`

Verify JWT token and get user information.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "lastLogin": "2023-01-01T12:00:00.000Z"
  }
}
```

### Logout User
**POST** `/auth/logout`

Logout user (client-side token removal).

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "message": "Logout successful"
}
```

## Flashcard Endpoints

### Get User Flashcards
**GET** `/flashcards`

Get paginated list of user's flashcards.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `search` (string): Search term for Russian word or translations
- `sortBy` (string): Sort field (created_at, updated_at, russian_word, difficulty_level)
- `sortOrder` (string): Sort order (ASC, DESC)

**Response (200):**
```json
{
  "flashcards": [
    {
      "card_id": 1,
      "russian_word": "привет",
      "english_translations": ["hello", "hi"],
      "ipa_transcription": "[prʲɪˈvʲet]",
      "audio_url": "/uploads/audio/hello.mp3",
      "example_sentence": "Привет, как дела?",
      "image_url": null,
      "difficulty_level": 1,
      "tags": ["greetings"],
      "created_at": "2023-01-01T00:00:00.000Z",
      "updated_at": "2023-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 100,
    "itemsPerPage": 20,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

### Search Global Database
**GET** `/flashcards/search-global`

Search the global flashcards database.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `q` (string): Search term (required)

**Response (200):**
```json
{
  "results": [
    {
      "global_card_id": 1,
      "russian_word": "привет",
      "english_translations": ["hello", "hi"],
      "ipa_transcription": "[prʲɪˈvʲet]",
      "audio_url": "/uploads/audio/hello.mp3",
      "example_sentences": ["Привет, как дела?"],
      "word_type": "interjection",
      "frequency_rank": 1
    }
  ]
}
```

### Get Single Flashcard
**GET** `/flashcards/:id`

Get a specific flashcard by ID.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "flashcard": {
    "card_id": 1,
    "russian_word": "привет",
    "english_translations": ["hello", "hi"],
    "ipa_transcription": "[prʲɪˈvʲet]",
    "audio_url": "/uploads/audio/hello.mp3",
    "example_sentence": "Привет, как дела?",
    "image_url": null,
    "difficulty_level": 1,
    "tags": ["greetings"],
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

### Create Flashcard
**POST** `/flashcards`

Create a new flashcard.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "russianWord": "привет",
  "englishTranslations": ["hello", "hi"],
  "ipaTranscription": "[prʲɪˈvʲet]",
  "exampleSentence": "Привет, как дела?",
  "imageUrl": "https://example.com/image.jpg",
  "difficultyLevel": 1,
  "tags": ["greetings"]
}
```

**Response (201):**
```json
{
  "message": "Flashcard created successfully",
  "flashcard": {
    "card_id": 1,
    "russian_word": "привет",
    "english_translations": ["hello", "hi"],
    "ipa_transcription": "[prʲɪˈvʲet]",
    "audio_url": "/uploads/audio/hello.mp3",
    "example_sentence": "Привет, как дела?",
    "image_url": "https://example.com/image.jpg",
    "difficulty_level": 1,
    "tags": ["greetings"],
    "created_at": "2023-01-01T00:00:00.000Z"
  }
}
```

### Update Flashcard
**PUT** `/flashcards/:id`

Update an existing flashcard.

**Headers:** `Authorization: Bearer <token>`

**Request Body:** Same as create flashcard

**Response (200):**
```json
{
  "message": "Flashcard updated successfully",
  "flashcard": { ... }
}
```

### Delete Flashcard
**DELETE** `/flashcards/:id`

Delete a flashcard.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "message": "Flashcard deleted successfully"
}
```

## Practice Endpoints

### Get Practice Session
**GET** `/practice/session`

Get a practice session with 12 cards due for review.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "sessionId": 123,
  "sessionDate": "2023-01-01T12:00:00.000Z",
  "cards": [
    {
      "cardId": 1,
      "russianWord": "привет",
      "englishTranslations": ["hello", "hi"],
      "ipaTranscription": "[prʲɪˈvʲet]",
      "audioUrl": "/uploads/audio/hello.mp3",
      "exampleSentence": "Привет, как дела?",
      "imageUrl": null,
      "difficultyLevel": 1,
      "practiceData": {
        "lastReviewed": "2023-01-01T00:00:00.000Z",
        "nextReview": "2023-01-01T12:00:00.000Z",
        "correctCount": 5,
        "incorrectCount": 2,
        "easeFactor": 2.5,
        "intervalDays": 3,
        "repetitionNumber": 3
      }
    }
  ],
  "totalCards": 12
}
```

### Submit Answer
**POST** `/practice/answer`

Submit an answer for a practice card.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "cardId": 1,
  "sessionId": 123,
  "userAnswer": "hello",
  "isCorrect": true,
  "responseTime": 5000
}
```

**Response (200):**
```json
{
  "message": "Answer submitted successfully",
  "isCorrect": true,
  "nextReview": "2023-01-04T12:00:00.000Z",
  "intervalDays": 6,
  "easeFactor": 2.6,
  "streakData": {
    "correctCount": 6,
    "incorrectCount": 2,
    "repetitionNumber": 4
  }
}
```

### Get Practice Statistics
**GET** `/practice/stats`

Get practice statistics for the user.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `timeframe` (string): Time period (1, 7, 30, 90, all) in days

**Response (200):**
```json
{
  "timeframe": "7",
  "sessions": {
    "totalSessions": 10,
    "totalCardsReviewed": 120,
    "totalCorrect": 96,
    "totalIncorrect": 24,
    "accuracy": 80,
    "avgSessionDuration": 15
  },
  "cards": {
    "totalCards": 50,
    "dueForReview": 12
  },
  "streak": {
    "studyDays": 5,
    "lastStudyDate": "2023-01-01T12:00:00.000Z"
  }
}
```

## User Endpoints

### Get User Profile
**GET** `/users/profile`

Get user profile and statistics.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "lastLogin": "2023-01-01T12:00:00.000Z",
    "emailVerified": false
  },
  "statistics": {
    "totalCards": 50,
    "dueCards": 12,
    "averageAccuracy": 85
  },
  "recentActivity": [
    {
      "session_date": "2023-01-01T12:00:00.000Z",
      "cards_reviewed": 12,
      "cards_correct": 10,
      "cards_incorrect": 2
    }
  ]
}
```

### Update User Profile
**PUT** `/users/profile`

Update user profile information.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>"
}
```

**Response (200):**
```json
{
  "message": "Profile updated successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "updatedAt": "2023-01-01T12:00:00.000Z",
    "emailVerified": false
  }
}
```

### Change Password
**PUT** `/users/password`

Change user password.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

**Response (200):**
```json
{
  "message": "Password changed successfully"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Valid email is required"
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "message": "Access token is required"
}
```

### 403 Forbidden
```json
{
  "message": "Access denied: You do not own this resource"
}
```

### 404 Not Found
```json
{
  "message": "Resource not found"
}
```

### 409 Conflict
```json
{
  "message": "User with this email already exists"
}
```

### 429 Too Many Requests
```json
{
  "message": "Too many requests from this IP, please try again later."
}
```

### 500 Internal Server Error
```json
{
  "message": "Internal server error"
}
```
