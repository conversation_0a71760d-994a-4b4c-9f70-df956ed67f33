-- Russian Flashcards Database Schema
-- PostgreSQL

-- Create database (run this separately)
-- CREATE DATABASE russian_flashcards;

-- Users table
CREATE TABLE users (
    user_id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    reset_token VARCHAR(255),
    reset_token_expires TIMESTAMP
);

-- Flashcards table
CREATE TABLE flashcards (
    card_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE, -- Deprecated: will be replaced by created_by
    russian_word VARCHAR(255) NOT NULL,
    english_translations JSONB NOT NULL, -- Array of translations
    ipa_transcription VARCHAR(500),
    audio_url VARCHAR(500) NOT NULL, -- Required for admin-created cards
    example_sentence VARCHAR(250),
    image_url VARCHAR(500),
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
    tags JSONB DEFAULT '[]', -- Array of tags for categorization

    -- New fields to match your interface
    part_of_speech VARCHAR(50), -- noun, verb, adjective, etc.
    number_type VARCHAR(20), -- singular, plural, etc.
    gender VARCHAR(20), -- masculine, feminine, neuter
    flashcard_sets JSONB DEFAULT '[]', -- Array of set names the card belongs to
    author_name VARCHAR(255),
    author_link VARCHAR(500),
    google_tts_audio_url VARCHAR(500), -- Google TTS generated audio
    use_google_tts BOOLEAN DEFAULT true, -- Whether to use Google TTS for this card

    created_by INTEGER REFERENCES users(user_id), -- Admin who created the card
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_public BOOLEAN DEFAULT false -- For future sharing feature
);

-- Practice sessions table
CREATE TABLE practice_sessions (
    session_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
    card_id INTEGER REFERENCES flashcards(card_id) ON DELETE CASCADE,
    last_reviewed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    next_review TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    correct_count INTEGER DEFAULT 0,
    incorrect_count INTEGER DEFAULT 0,
    ease_factor DECIMAL(3,2) DEFAULT 2.50, -- For SuperMemo algorithm
    interval_days INTEGER DEFAULT 1,
    repetition_number INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, card_id)
);

-- Session history table for tracking practice sessions
CREATE TABLE session_history (
    history_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
    session_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cards_reviewed INTEGER DEFAULT 0,
    cards_correct INTEGER DEFAULT 0,
    cards_incorrect INTEGER DEFAULT 0,
    session_duration_minutes INTEGER,
    session_type VARCHAR(50) DEFAULT 'practice' -- practice, review, etc.
);

-- Word requests table for user word requests
CREATE TABLE word_requests (
    request_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    russian_word VARCHAR(100) NOT NULL,
    comments TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    admin_notes TEXT,
    processed_by INTEGER REFERENCES users(user_id),
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- User card overlays table for personal notes and examples
CREATE TABLE user_card_overlays (
    overlay_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    card_id INTEGER NOT NULL REFERENCES flashcards(card_id) ON DELETE CASCADE,
    notes TEXT,
    user_examples TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, card_id)
);

-- Practice sets table for admin-curated practice sets
CREATE TABLE practice_sets (
    set_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    card_ids JSONB NOT NULL,
    criteria JSONB, -- {"tags": ["food", "accusative"], "difficulty": [1,2,3]}
    assigned_user_ids JSONB, -- null = all users, array = specific users
    created_by INTEGER NOT NULL REFERENCES users(user_id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Global flashcard database for shared words
CREATE TABLE global_flashcards (
    global_card_id SERIAL PRIMARY KEY,
    russian_word VARCHAR(255) UNIQUE NOT NULL,
    english_translations JSONB NOT NULL,
    ipa_transcription VARCHAR(500),
    audio_url VARCHAR(500),
    example_sentences JSONB DEFAULT '[]', -- Multiple example sentences
    frequency_rank INTEGER, -- Word frequency ranking
    word_type VARCHAR(50), -- noun, verb, adjective, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usage_count INTEGER DEFAULT 0 -- How many users have this word
);

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_is_admin ON users(is_admin);
CREATE INDEX idx_flashcards_user_id ON flashcards(user_id);
CREATE INDEX idx_flashcards_created_by ON flashcards(created_by);
CREATE INDEX idx_flashcards_russian_word ON flashcards(russian_word);
CREATE INDEX idx_practice_sessions_user_id ON practice_sessions(user_id);
CREATE INDEX idx_practice_sessions_next_review ON practice_sessions(next_review);
CREATE INDEX idx_practice_sessions_user_card ON practice_sessions(user_id, card_id);
CREATE INDEX idx_session_history_user_date ON session_history(user_id, session_date);
CREATE INDEX idx_global_flashcards_word ON global_flashcards(russian_word);
CREATE INDEX idx_word_requests_status ON word_requests(status);
CREATE INDEX idx_word_requests_user_id ON word_requests(user_id);
CREATE INDEX idx_overlays_user_card ON user_card_overlays(user_id, card_id);
CREATE INDEX idx_practice_sets_active ON practice_sets(is_active);
CREATE INDEX idx_practice_sets_created_by ON practice_sets(created_by);

-- Full-text search index for Russian words
CREATE INDEX idx_flashcards_russian_word_gin ON flashcards USING gin(to_tsvector('russian', russian_word));
CREATE INDEX idx_global_flashcards_russian_word_gin ON global_flashcards USING gin(to_tsvector('russian', russian_word));

-- Triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_flashcards_updated_at BEFORE UPDATE ON flashcards
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_practice_sessions_updated_at BEFORE UPDATE ON practice_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_global_flashcards_updated_at BEFORE UPDATE ON global_flashcards
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Sample data for testing
INSERT INTO global_flashcards (russian_word, english_translations, ipa_transcription, example_sentences, frequency_rank, word_type) VALUES
('привет', '["hello", "hi"]', '[prʲɪˈvʲet]', '["Привет, как дела?", "Привет! Рад тебя видеть!"]', 1, 'interjection'),
('спасибо', '["thank you", "thanks"]', '[spɐˈsʲibə]', '["Спасибо за помощь!", "Большое спасибо!"]', 2, 'interjection'),
('дом', '["house", "home"]', '[dom]', '["Мой дом очень красивый.", "Я иду домой."]', 3, 'noun'),
('вода', '["water"]', '[vɐˈda]', '["Мне нужна вода.", "Вода очень холодная."]', 4, 'noun'),
('читать', '["to read"]', '[tʂɪˈtatʲ]', '["Я люблю читать книги.", "Он читает газету."]', 5, 'verb');
