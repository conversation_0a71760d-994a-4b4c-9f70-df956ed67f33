# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*.lcov
.nyc_output

# Build outputs
build/
dist/
.next/
.nuxt/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
docs/
*.md

# Test files
tests/
__tests__/
*.test.js
*.spec.js
jest.config.js

# Development tools
.eslintrc*
.prettierrc*
.babelrc*

# Temporary files
tmp/
temp/

# Audio uploads (will be mounted as volume)
uploads/audio/*
!uploads/audio/.gitkeep

# Database files
*.sql.backup
*.dump

# Scripts
scripts/
start.sh
start.bat
install-tools.bat
