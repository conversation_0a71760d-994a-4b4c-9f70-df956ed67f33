# Podman Setup Guide for Russian Flashcards

This guide will help you install and configure <PERSON><PERSON> to run the Russian Flashcards platform.

## Why <PERSON>dman?

- **Daemonless**: No background daemon required
- **Rootless**: Can run without root privileges
- **Docker-compatible**: Uses same commands and formats
- **Lightweight**: Lower resource usage than Docker
- **Secure**: Better security model

## Installation

### Windows 11

#### Option 1: Podman Desktop (Recommended)
1. Go to https://podman-desktop.io/
2. Download "Podman Desktop for Windows"
3. Run the installer with default settings
4. Start Podman Desktop after installation

#### Option 2: Command Line Installation
```powershell
# Using winget
winget install RedHat.Podman-Desktop

# Or using Chocolatey (if available)
choco install podman-desktop
```

#### Option 3: Manual Installation
1. Go to https://github.com/containers/podman/releases
2. Download the Windows installer (.msi file)
3. Run the installer
4. Add Podman to your PATH if not done automatically

### Verify Installation

```cmd
podman --version
podman machine list
```

## Initial Setup

### 1. Initialize Podman Machine (Windows/macOS)
```cmd
# Create and start a Podman machine
podman machine init
podman machine start
```

### 2. Test Podman
```cmd
# Test with a simple container
podman run hello-world

# Check if it's working
podman ps -a
```

### 3. Install podman-compose (Optional)
```cmd
# Using pip (if Python is installed)
pip install podman-compose

# Or download from GitHub releases
# https://github.com/containers/podman-compose/releases
```

## Running Russian Flashcards

### Quick Start
1. **Run the startup script**:
   ```cmd
   podman-start.bat
   ```

2. **Or manually**:
   ```cmd
   # Copy environment file
   copy .env.docker .env
   
   # Start services
   podman-compose -f podman-compose.yml up --build -d
   
   # Or if podman-compose is not available
   podman compose -f podman-compose.yml up --build -d
   ```

3. **Access the application**:
   - Application: http://localhost:3000
   - Database: localhost:5432

### Manual Container Management

#### Build and Run Individual Containers

```cmd
# Build the application image
podman build -t russian-flashcards -f Containerfile .

# Run PostgreSQL
podman run -d --name postgres-db \
  -e POSTGRES_DB=russian_flashcards_dev \
  -e POSTGRES_USER=dev_user \
  -e POSTGRES_PASSWORD=dev_password \
  -p 5432:5432 \
  docker.io/postgres:15-alpine

# Run Redis
podman run -d --name redis-cache \
  -p 6379:6379 \
  docker.io/redis:7-alpine

# Run the application
podman run -d --name flashcards-app \
  -p 3000:3000 \
  -e DB_HOST=host.containers.internal \
  -e REDIS_HOST=host.containers.internal \
  --env-file .env \
  russian-flashcards
```

#### Using Pods (Podman-specific feature)

```cmd
# Create a pod
podman pod create --name flashcards-pod -p 3000:3000 -p 5432:5432 -p 6379:6379

# Run containers in the pod
podman run -d --pod flashcards-pod --name postgres \
  -e POSTGRES_DB=russian_flashcards_dev \
  -e POSTGRES_USER=dev_user \
  -e POSTGRES_PASSWORD=dev_password \
  docker.io/postgres:15-alpine

podman run -d --pod flashcards-pod --name redis \
  docker.io/redis:7-alpine

podman run -d --pod flashcards-pod --name app \
  -e DB_HOST=localhost \
  -e REDIS_HOST=localhost \
  --env-file .env \
  russian-flashcards
```

## Useful Commands

### Container Management
```cmd
# List running containers
podman ps

# List all containers
podman ps -a

# View logs
podman logs russian-flashcards-app

# Follow logs
podman logs -f russian-flashcards-app

# Enter container
podman exec -it russian-flashcards-app /bin/bash

# Stop containers
podman stop russian-flashcards-app

# Remove containers
podman rm russian-flashcards-app
```

### Image Management
```cmd
# List images
podman images

# Remove image
podman rmi russian-flashcards

# Pull image
podman pull docker.io/postgres:15-alpine

# Build image
podman build -t russian-flashcards .
```

### Pod Management (Podman-specific)
```cmd
# List pods
podman pod list

# Stop pod
podman pod stop flashcards-pod

# Remove pod
podman pod rm flashcards-pod
```

### Machine Management (Windows/macOS)
```cmd
# List machines
podman machine list

# Start machine
podman machine start

# Stop machine
podman machine stop

# SSH into machine
podman machine ssh
```

## Troubleshooting

### Common Issues

#### 1. Podman Machine Not Running
```cmd
# Start the machine
podman machine start

# If it fails, try recreating
podman machine stop
podman machine rm
podman machine init
podman machine start
```

#### 2. Port Already in Use
```cmd
# Check what's using the port
netstat -ano | findstr :3000

# Kill the process or use different ports in podman-compose.yml
```

#### 3. Permission Issues
```cmd
# On Windows, run PowerShell as Administrator
# On Linux, ensure user is in podman group
sudo usermod -aG podman $USER
```

#### 4. Container Won't Start
```cmd
# Check logs
podman logs container-name

# Check container status
podman ps -a

# Inspect container
podman inspect container-name
```

#### 5. Network Issues
```cmd
# Reset Podman networking
podman system reset

# Or recreate machine
podman machine rm
podman machine init
```

### Performance Tips

1. **Allocate more resources to Podman machine**:
   ```cmd
   podman machine init --cpus 4 --memory 4096
   ```

2. **Use volumes for better performance**:
   - Avoid mounting large directories
   - Use named volumes for data persistence

3. **Clean up regularly**:
   ```cmd
   # Remove unused containers
   podman container prune
   
   # Remove unused images
   podman image prune
   
   # Remove unused volumes
   podman volume prune
   ```

## Environment Configuration

### .env File Setup
Copy `.env.docker` to `.env` and configure:

```env
# Required for audio features
GOOGLE_TTS_API_KEY=your_api_key_here

# Optional
FORVO_API_KEY=your_forvo_key_here
YANDEX_DICTIONARY_API_KEY=your_yandex_key_here
```

### Database Access
- **Host**: localhost
- **Port**: 5432
- **Database**: russian_flashcards_dev
- **Username**: dev_user
- **Password**: dev_password

## Next Steps

1. **Install Podman** using one of the methods above
2. **Run the setup script**: `podman-start.bat`
3. **Configure API keys** in `.env` file
4. **Access the application** at http://localhost:3000
5. **Start learning Russian!** 🇷🇺

## Getting Help

- **Podman Documentation**: https://docs.podman.io/
- **Podman Desktop**: https://podman-desktop.io/docs
- **GitHub Issues**: https://github.com/containers/podman/issues

For project-specific issues, check the main README.md file.
