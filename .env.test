# Test Environment Configuration
NODE_ENV=test
PORT=3001

# Test Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=russian_flashcards_test
DB_USER=postgres
DB_PASSWORD=
DB_SSL=false

# JWT Configuration for Testing
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h

# Disable external APIs during testing
GOOGLE_TTS_API_KEY=
FORVO_API_KEY=
YANDEX_DICTIONARY_API_KEY=

# Email Configuration (disabled for testing)
EMAIL_HOST=
EMAIL_PORT=
EMAIL_USER=
EMAIL_PASSWORD=
EMAIL_FROM=

# Rate Limiting (more lenient for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
