#!/usr/bin/env node

/**
 * Database migration script for deployment
 * This script runs database migrations and seeds initial data
 */

const fs = require('fs').promises;
const path = require('path');
const { query, testConnection, closePool } = require('../config/database');

async function runMigrations() {
    console.log('Starting database migrations...');
    
    try {
        // Test database connection
        const connected = await testConnection();
        if (!connected) {
            throw new Error('Database connection failed');
        }
        
        // Check if tables exist
        const tablesResult = await query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('users', 'flashcards', 'practice_sessions', 'session_history', 'global_flashcards')
        `);
        
        const existingTables = tablesResult.rows.map(row => row.table_name);
        const requiredTables = ['users', 'flashcards', 'practice_sessions', 'session_history', 'global_flashcards'];
        const missingTables = requiredTables.filter(table => !existingTables.includes(table));
        
        if (missingTables.length > 0) {
            console.log(`Missing tables: ${missingTables.join(', ')}`);
            console.log('Running schema creation...');
            
            // Read and execute schema file
            const schemaPath = path.join(__dirname, '..', 'database', 'schema.sql');
            const schemaSQL = await fs.readFile(schemaPath, 'utf8');
            
            // Split by semicolon and execute each statement
            const statements = schemaSQL
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
            
            for (const statement of statements) {
                try {
                    await query(statement);
                } catch (error) {
                    // Ignore errors for statements that might already exist
                    if (!error.message.includes('already exists')) {
                        console.warn('Migration warning:', error.message);
                    }
                }
            }
            
            console.log('Schema creation completed');
        } else {
            console.log('All required tables exist');
        }
        
        // Check if global flashcards are seeded
        const globalCardsResult = await query('SELECT COUNT(*) as count FROM global_flashcards');
        const globalCardsCount = parseInt(globalCardsResult.rows[0].count);
        
        if (globalCardsCount === 0) {
            console.log('Seeding global flashcards...');
            await seedGlobalFlashcards();
        } else {
            console.log(`Global flashcards already seeded (${globalCardsCount} cards)`);
        }
        
        console.log('Database migrations completed successfully');
        
    } catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    } finally {
        await closePool();
    }
}

async function seedGlobalFlashcards() {
    const commonWords = [
        {
            word: 'привет',
            translations: ['hello', 'hi'],
            ipa: '[prʲɪˈvʲet]',
            examples: ['Привет, как дела?', 'Привет! Рад тебя видеть!'],
            type: 'interjection',
            rank: 1
        },
        {
            word: 'спасибо',
            translations: ['thank you', 'thanks'],
            ipa: '[spɐˈsʲibə]',
            examples: ['Спасибо за помощь!', 'Большое спасибо!'],
            type: 'interjection',
            rank: 2
        },
        {
            word: 'дом',
            translations: ['house', 'home'],
            ipa: '[dom]',
            examples: ['Мой дом очень красивый.', 'Я иду домой.'],
            type: 'noun',
            rank: 3
        },
        {
            word: 'вода',
            translations: ['water'],
            ipa: '[vɐˈda]',
            examples: ['Мне нужна вода.', 'Вода очень холодная.'],
            type: 'noun',
            rank: 4
        },
        {
            word: 'читать',
            translations: ['to read'],
            ipa: '[tʂɪˈtatʲ]',
            examples: ['Я люблю читать книги.', 'Он читает газету.'],
            type: 'verb',
            rank: 5
        },
        {
            word: 'работать',
            translations: ['to work'],
            ipa: '[rɐˈbotətʲ]',
            examples: ['Я работаю в офисе.', 'Она работает врачом.'],
            type: 'verb',
            rank: 6
        },
        {
            word: 'хорошо',
            translations: ['good', 'well', 'fine'],
            ipa: '[xɐrɐˈʂo]',
            examples: ['Всё хорошо.', 'Он хорошо говорит по-русски.'],
            type: 'adverb',
            rank: 7
        },
        {
            word: 'время',
            translations: ['time'],
            ipa: '[ˈvrʲemʲə]',
            examples: ['У меня нет времени.', 'Время летит быстро.'],
            type: 'noun',
            rank: 8
        },
        {
            word: 'знать',
            translations: ['to know'],
            ipa: '[znatʲ]',
            examples: ['Я знаю этого человека.', 'Она знает английский язык.'],
            type: 'verb',
            rank: 9
        },
        {
            word: 'говорить',
            translations: ['to speak', 'to talk', 'to say'],
            ipa: '[ɡəvɐˈrʲitʲ]',
            examples: ['Он говорит по-русски.', 'Мы говорим о работе.'],
            type: 'verb',
            rank: 10
        }
    ];
    
    for (const word of commonWords) {
        try {
            await query(
                `INSERT INTO global_flashcards 
                (russian_word, english_translations, ipa_transcription, example_sentences, word_type, frequency_rank, usage_count)
                VALUES ($1, $2, $3, $4, $5, $6, 0)`,
                [
                    word.word,
                    JSON.stringify(word.translations),
                    word.ipa,
                    JSON.stringify(word.examples),
                    word.type,
                    word.rank
                ]
            );
        } catch (error) {
            console.warn(`Failed to seed word "${word.word}":`, error.message);
        }
    }
    
    console.log(`Seeded ${commonWords.length} global flashcards`);
}

// Run migrations if this script is executed directly
if (require.main === module) {
    runMigrations();
}

module.exports = { runMigrations, seedGlobalFlashcards };
