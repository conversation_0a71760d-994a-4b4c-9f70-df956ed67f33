const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// Middleware to authenticate JWT tokens
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({
                message: 'Access token is required'
            });
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Check if user still exists and is active
        const result = await query(
            'SELECT user_id, email, is_active FROM users WHERE user_id = $1',
            [decoded.userId]
        );

        if (result.rows.length === 0) {
            return res.status(401).json({
                message: 'User not found'
            });
        }

        const user = result.rows[0];

        if (!user.is_active) {
            return res.status(401).json({
                message: 'User account is deactivated'
            });
        }

        // Add user info to request object
        req.userId = user.user_id;
        req.userEmail = user.email;
        
        next();

    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                message: 'Invalid token'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                message: 'Token has expired'
            });
        }

        console.error('Authentication error:', error);
        return res.status(500).json({
            message: 'Internal server error during authentication'
        });
    }
};

// Optional authentication middleware (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return next();
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        const result = await query(
            'SELECT user_id, email, is_active FROM users WHERE user_id = $1',
            [decoded.userId]
        );

        if (result.rows.length > 0 && result.rows[0].is_active) {
            req.userId = result.rows[0].user_id;
            req.userEmail = result.rows[0].email;
        }

        next();

    } catch (error) {
        // Ignore token errors for optional auth
        next();
    }
};

// Middleware to check if user owns a resource
const checkResourceOwnership = (resourceType) => {
    return async (req, res, next) => {
        try {
            const resourceId = req.params.id || req.params.cardId;
            
            if (!resourceId) {
                return res.status(400).json({
                    message: 'Resource ID is required'
                });
            }

            let query_text;
            let params = [resourceId, req.userId];

            switch (resourceType) {
                case 'flashcard':
                    query_text = 'SELECT user_id FROM flashcards WHERE card_id = $1 AND user_id = $2';
                    break;
                case 'practice_session':
                    query_text = 'SELECT user_id FROM practice_sessions WHERE session_id = $1 AND user_id = $2';
                    break;
                default:
                    return res.status(400).json({
                        message: 'Invalid resource type'
                    });
            }

            const result = await query(query_text, params);

            if (result.rows.length === 0) {
                return res.status(403).json({
                    message: 'Access denied: You do not own this resource'
                });
            }

            next();

        } catch (error) {
            console.error('Resource ownership check error:', error);
            return res.status(500).json({
                message: 'Internal server error during authorization'
            });
        }
    };
};

// Admin middleware (for future admin features)
const requireAdmin = async (req, res, next) => {
    try {
        const result = await query(
            'SELECT is_admin FROM users WHERE user_id = $1',
            [req.userId]
        );

        if (result.rows.length === 0 || !result.rows[0].is_admin) {
            return res.status(403).json({
                message: 'Admin access required'
            });
        }

        next();

    } catch (error) {
        console.error('Admin check error:', error);
        return res.status(500).json({
            message: 'Internal server error during admin check'
        });
    }
};

module.exports = {
    authenticateToken,
    optionalAuth,
    checkResourceOwnership,
    requireAdmin
};
