@echo off
echo ============================================
echo Russian Flashcards - Docker Quick Start
echo ============================================
echo.

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running or not installed.
    echo Please start Docker Desktop and try again.
    echo.
    echo To install Docker Desktop:
    echo 1. Go to https://www.docker.com/products/docker-desktop/
    echo 2. Download and install Docker Desktop for Windows
    echo 3. Start Docker Desktop
    echo 4. Run this script again
    pause
    exit /b 1
)

echo [✓] Docker is running

REM Check if .env file exists
if not exist .env (
    echo.
    echo Creating .env file from template...
    copy .env.docker .env
    echo [✓] Created .env file
    echo.
    echo IMPORTANT: Please edit .env file and add your API keys:
    echo - GOOGLE_TTS_API_KEY (for audio generation)
    echo - FORVO_API_KEY (optional, for audio fallback)
    echo.
    echo Press any key to continue with default settings...
    pause >nul
) else (
    echo [✓] .env file exists
)

echo.
echo Starting Russian Flashcards in Docker...
echo.

REM Start development environment
echo Starting development environment...
docker-compose -f docker-compose.dev.yml up --build -d

if %errorlevel% == 0 (
    echo.
    echo ============================================
    echo 🎉 Russian Flashcards is starting up!
    echo ============================================
    echo.
    echo Services:
    echo - Application: http://localhost:3001
    echo - Database: localhost:5433 (PostgreSQL)
    echo - pgAdmin: http://localhost:8080
    echo   - Email: <EMAIL>
    echo   - Password: admin123
    echo - Redis: localhost:6380
    echo.
    echo Logs:
    echo - View all logs: docker-compose -f docker-compose.dev.yml logs -f
    echo - View app logs: docker-compose -f docker-compose.dev.yml logs -f app
    echo.
    echo Management:
    echo - Stop: docker-compose -f docker-compose.dev.yml down
    echo - Restart: docker-compose -f docker-compose.dev.yml restart
    echo - Rebuild: docker-compose -f docker-compose.dev.yml up --build
    echo.
    echo The application will be ready in about 30-60 seconds...
    echo Opening browser in 10 seconds...
    
    timeout /t 10 /nobreak >nul
    start http://localhost:3001
    
) else (
    echo.
    echo [ERROR] Failed to start containers.
    echo Please check the error messages above.
    echo.
    echo Common solutions:
    echo 1. Make sure Docker Desktop is running
    echo 2. Check if ports 3001, 5433, 6380, 8080 are available
    echo 3. Try: docker-compose -f docker-compose.dev.yml down
    echo 4. Then run this script again
)

echo.
echo Press any key to exit...
pause >nul
