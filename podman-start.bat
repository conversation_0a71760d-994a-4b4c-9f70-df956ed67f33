@echo off
echo ============================================
echo Russian Flashcards - Podman Quick Start
echo ============================================
echo.

REM Check if <PERSON>dman is installed
podman --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] <PERSON><PERSON> is not installed or not in PATH.
    echo.
    echo To install Podman on Windows:
    echo 1. Go to https://podman.io/getting-started/installation
    echo 2. Download Podman Desktop for Windows
    echo 3. Or use winget: winget install RedHat.Podman-Desktop
    echo 4. Restart your terminal and try again
    pause
    exit /b 1
)

echo [✓] Podman is installed
podman --version

REM Check if podman-compose is available
podman-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo [INFO] podman-compose not found, using podman compose instead
    set COMPOSE_CMD=podman compose
) else (
    echo [✓] podman-compose is available
    set COMPOSE_CMD=podman-compose
)

REM Check if .env file exists
if not exist .env (
    echo.
    echo Creating .env file from template...
    copy .env.docker .env
    echo [✓] Created .env file
    echo.
    echo IMPORTANT: Please edit .env file and add your API keys:
    echo - GOOGLE_TTS_API_KEY (for audio generation)
    echo - FORVO_API_KEY (optional, for audio fallback)
    echo.
    echo Press any key to continue with default settings...
    pause >nul
) else (
    echo [✓] .env file exists
)

echo.
echo Starting Russian Flashcards with Podman...
echo.

REM Start the Podman machine if needed (Windows/macOS)
echo Ensuring Podman machine is running...
podman machine start >nul 2>&1

REM Stop any existing containers
echo Stopping any existing containers...
%COMPOSE_CMD% -f podman-compose.yml down >nul 2>&1

REM Start development environment
echo Starting development environment...
%COMPOSE_CMD% -f podman-compose.yml up --build -d

if %errorlevel% == 0 (
    echo.
    echo ============================================
    echo 🎉 Russian Flashcards is starting up!
    echo ============================================
    echo.
    echo Services:
    echo - Application: http://localhost:3000
    echo - Database: localhost:5432 (PostgreSQL)
    echo - Redis: localhost:6379
    echo.
    echo Container Management:
    echo - View logs: %COMPOSE_CMD% -f podman-compose.yml logs -f
    echo - View app logs: %COMPOSE_CMD% -f podman-compose.yml logs -f app
    echo - Stop: %COMPOSE_CMD% -f podman-compose.yml down
    echo - Restart: %COMPOSE_CMD% -f podman-compose.yml restart
    echo - Rebuild: %COMPOSE_CMD% -f podman-compose.yml up --build
    echo.
    echo Podman Commands:
    echo - List containers: podman ps
    echo - Container logs: podman logs russian-flashcards-app
    echo - Enter container: podman exec -it russian-flashcards-app /bin/bash
    echo.
    echo The application will be ready in about 30-60 seconds...
    echo.
    
    REM Wait a bit for containers to start
    echo Waiting for containers to start...
    timeout /t 15 /nobreak >nul
    
    REM Check if app is responding
    echo Checking if application is ready...
    curl -f http://localhost:3000/api/health >nul 2>&1
    if %errorlevel% == 0 (
        echo [✓] Application is ready!
        echo Opening browser...
        start http://localhost:3000
    ) else (
        echo [INFO] Application is still starting up...
        echo Please wait a moment and visit: http://localhost:3000
    )
    
) else (
    echo.
    echo [ERROR] Failed to start containers.
    echo Please check the error messages above.
    echo.
    echo Common solutions:
    echo 1. Make sure Podman machine is running: podman machine start
    echo 2. Check if ports 3000, 5432, 6379 are available
    echo 3. Try: %COMPOSE_CMD% -f podman-compose.yml down
    echo 4. Then run this script again
    echo.
    echo Debug commands:
    echo - Check Podman status: podman machine list
    echo - View container status: podman ps -a
    echo - View logs: %COMPOSE_CMD% -f podman-compose.yml logs
)

echo.
echo Press any key to exit...
pause >nul
