-- Migration 001: Add Admin Architecture
-- This migration adds the admin-centric architecture to the existing database

-- Step 1: Add is_admin column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT false;

-- Step 2: Add created_by column to flashcards table
ALTER TABLE flashcards ADD COLUMN IF NOT EXISTS created_by INTEGER REFERENCES users(user_id);

-- Step 3: Create word_requests table
CREATE TABLE IF NOT EXISTS word_requests (
    request_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    russian_word VARCHAR(100) NOT NULL,
    comments TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    admin_notes TEXT,
    processed_by INTEGER REFERENCES users(user_id),
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

-- Step 4: Create user_card_overlays table
CREATE TABLE IF NOT EXISTS user_card_overlays (
    overlay_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    card_id INTEGER NOT NULL REFERENCES flashcards(card_id) ON DELETE CASCADE,
    notes TEXT,
    user_examples TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, card_id)
);

-- Step 5: Create practice_sets table
CREATE TABLE IF NOT EXISTS practice_sets (
    set_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    card_ids JSONB NOT NULL,
    criteria JSONB, -- {"tags": ["food", "accusative"], "difficulty": [1,2,3]}
    assigned_user_ids JSONB, -- null = all users, array = specific users
    created_by INTEGER NOT NULL REFERENCES users(user_id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Step 6: Create indexes
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON users(is_admin);
CREATE INDEX IF NOT EXISTS idx_flashcards_created_by ON flashcards(created_by);
CREATE INDEX IF NOT EXISTS idx_word_requests_status ON word_requests(status);
CREATE INDEX IF NOT EXISTS idx_word_requests_user_id ON word_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_overlays_user_card ON user_card_overlays(user_id, card_id);
CREATE INDEX IF NOT EXISTS idx_practice_sets_active ON practice_sets(is_active);
CREATE INDEX IF NOT EXISTS idx_practice_sets_created_by ON practice_sets(created_by);

-- Step 7: Data migration - Promote first user to admin
DO $$
BEGIN
    -- Promote the first user to admin if no admin exists
    IF NOT EXISTS (SELECT 1 FROM users WHERE is_admin = true) THEN
        UPDATE users 
        SET is_admin = true 
        WHERE user_id = (SELECT user_id FROM users ORDER BY created_at LIMIT 1);
    END IF;
END $$;

-- Step 8: Data migration - Set created_by for existing flashcards
DO $$
DECLARE
    admin_user_id INTEGER;
BEGIN
    -- Get the admin user ID
    SELECT user_id INTO admin_user_id FROM users WHERE is_admin = true LIMIT 1;
    
    -- Update existing flashcards to be created by admin
    UPDATE flashcards 
    SET created_by = admin_user_id 
    WHERE created_by IS NULL AND admin_user_id IS NOT NULL;
END $$;

-- Step 9: Create default practice set from existing cards
DO $$
DECLARE
    admin_user_id INTEGER;
    card_ids_json JSONB;
BEGIN
    -- Get the admin user ID
    SELECT user_id INTO admin_user_id FROM users WHERE is_admin = true LIMIT 1;
    
    -- Get first 12 cards as JSON array
    SELECT json_agg(card_id) INTO card_ids_json
    FROM (
        SELECT card_id FROM flashcards 
        WHERE audio_url IS NOT NULL 
        ORDER BY created_at 
        LIMIT 12
    ) subquery;
    
    -- Create default practice set if we have cards and admin user
    IF admin_user_id IS NOT NULL AND card_ids_json IS NOT NULL THEN
        INSERT INTO practice_sets (name, description, card_ids, assigned_user_ids, created_by)
        VALUES (
            'Default Set',
            'Migrated from existing cards',
            card_ids_json,
            null, -- Available to all users
            admin_user_id
        )
        ON CONFLICT DO NOTHING;
    END IF;
END $$;

-- Step 10: Add triggers for updated_at on new tables
CREATE TRIGGER update_user_card_overlays_updated_at 
    BEFORE UPDATE ON user_card_overlays
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_practice_sets_updated_at 
    BEFORE UPDATE ON practice_sets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Migration completed successfully
SELECT 'Migration 001 completed successfully' as status;
