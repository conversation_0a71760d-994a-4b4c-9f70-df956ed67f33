const express = require('express');
const { body, validationResult, query: expressQuery } = require('express-validator');
const { query, transaction } = require('../config/database');
const { authenticateToken, checkResourceOwnership } = require('../middleware/auth');
const { generateAudio } = require('../services/audioService');
const { searchGlobalWord } = require('../services/dictionaryService');

const router = express.Router();

// Validation rules
const flashcardValidation = [
    body('russianWord').trim().notEmpty().withMessage('Russian word is required'),
    body('englishTranslations').isArray({ min: 1 }).withMessage('At least one English translation is required'),
    body('englishTranslations.*').trim().notEmpty().withMessage('Translation cannot be empty'),
    body('ipaTranscription').optional().trim(),
    body('exampleSentence').optional().trim().isLength({ max: 250 }).withMessage('Example sentence must be 250 characters or less'),
    body('imageUrl').optional().isURL().withMessage('Image URL must be valid'),
    body('difficultyLevel').optional().isInt({ min: 1, max: 5 }).withMessage('Difficulty level must be between 1 and 5'),
    body('tags').optional().isArray().withMessage('Tags must be an array')
];

// Get all flashcards for authenticated user
router.get('/', authenticateToken, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const search = req.query.search || '';
        const sortBy = req.query.sortBy || 'created_at';
        const sortOrder = req.query.sortOrder || 'DESC';
        const offset = (page - 1) * limit;

        // Build search condition
        let searchCondition = '';
        let queryParams = [req.userId, limit, offset];
        
        if (search) {
            searchCondition = `AND (russian_word ILIKE $4 OR english_translations::text ILIKE $4)`;
            queryParams.push(`%${search}%`);
        }

        // Validate sort parameters
        const allowedSortFields = ['created_at', 'updated_at', 'russian_word', 'difficulty_level'];
        const allowedSortOrders = ['ASC', 'DESC'];
        
        const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
        const validSortOrder = allowedSortOrders.includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

        const flashcardsQuery = `
            SELECT 
                card_id,
                russian_word,
                english_translations,
                ipa_transcription,
                audio_url,
                example_sentence,
                image_url,
                difficulty_level,
                tags,
                created_at,
                updated_at
            FROM flashcards 
            WHERE user_id = $1 ${searchCondition}
            ORDER BY ${validSortBy} ${validSortOrder}
            LIMIT $2 OFFSET $3
        `;

        const countQuery = `
            SELECT COUNT(*) as total 
            FROM flashcards 
            WHERE user_id = $1 ${searchCondition}
        `;

        const [flashcardsResult, countResult] = await Promise.all([
            query(flashcardsQuery, queryParams),
            query(countQuery, search ? [req.userId, `%${search}%`] : [req.userId])
        ]);

        const total = parseInt(countResult.rows[0].total);
        const totalPages = Math.ceil(total / limit);

        res.json({
            flashcards: flashcardsResult.rows,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        });

    } catch (error) {
        console.error('Get flashcards error:', error);
        res.status(500).json({
            message: 'Internal server error while fetching flashcards'
        });
    }
});

// Search global flashcards database
router.get('/search-global', authenticateToken, async (req, res) => {
    try {
        const searchTerm = req.query.q;
        
        if (!searchTerm) {
            return res.status(400).json({
                message: 'Search term is required'
            });
        }

        const result = await query(
            `SELECT 
                global_card_id,
                russian_word,
                english_translations,
                ipa_transcription,
                audio_url,
                example_sentences,
                word_type,
                frequency_rank
            FROM global_flashcards 
            WHERE russian_word ILIKE $1 
            ORDER BY frequency_rank ASC NULLS LAST, russian_word
            LIMIT 10`,
            [`%${searchTerm}%`]
        );

        res.json({
            results: result.rows
        });

    } catch (error) {
        console.error('Global search error:', error);
        res.status(500).json({
            message: 'Internal server error during global search'
        });
    }
});

// Get single flashcard
router.get('/:id', authenticateToken, checkResourceOwnership('flashcard'), async (req, res) => {
    try {
        const result = await query(
            `SELECT 
                card_id,
                russian_word,
                english_translations,
                ipa_transcription,
                audio_url,
                example_sentence,
                image_url,
                difficulty_level,
                tags,
                created_at,
                updated_at
            FROM flashcards 
            WHERE card_id = $1 AND user_id = $2`,
            [req.params.id, req.userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({
                message: 'Flashcard not found'
            });
        }

        res.json({
            flashcard: result.rows[0]
        });

    } catch (error) {
        console.error('Get flashcard error:', error);
        res.status(500).json({
            message: 'Internal server error while fetching flashcard'
        });
    }
});

// Create new flashcard
router.post('/', authenticateToken, flashcardValidation, async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            russianWord,
            englishTranslations,
            ipaTranscription,
            exampleSentence,
            imageUrl,
            difficultyLevel = 1,
            tags = []
        } = req.body;

        // Check if user already has this word
        const existingCard = await query(
            'SELECT card_id FROM flashcards WHERE user_id = $1 AND russian_word = $2',
            [req.userId, russianWord]
        );

        if (existingCard.rows.length > 0) {
            return res.status(409).json({
                message: 'You already have a flashcard for this word'
            });
        }

        // Generate audio URL
        let audioUrl = null;
        try {
            audioUrl = await generateAudio(russianWord);
        } catch (audioError) {
            console.warn('Audio generation failed:', audioError.message);
            // Continue without audio - it's not critical
        }

        // Create flashcard
        const result = await query(
            `INSERT INTO flashcards 
            (user_id, russian_word, english_translations, ipa_transcription, audio_url, example_sentence, image_url, difficulty_level, tags)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING card_id, russian_word, english_translations, ipa_transcription, audio_url, example_sentence, image_url, difficulty_level, tags, created_at`,
            [req.userId, russianWord, JSON.stringify(englishTranslations), ipaTranscription, audioUrl, exampleSentence, imageUrl, difficultyLevel, JSON.stringify(tags)]
        );

        const newCard = result.rows[0];

        // Initialize practice session for this card
        await query(
            `INSERT INTO practice_sessions (user_id, card_id, next_review)
            VALUES ($1, $2, CURRENT_TIMESTAMP)`,
            [req.userId, newCard.card_id]
        );

        res.status(201).json({
            message: 'Flashcard created successfully',
            flashcard: newCard
        });

    } catch (error) {
        console.error('Create flashcard error:', error);
        res.status(500).json({
            message: 'Internal server error while creating flashcard'
        });
    }
});

// Update flashcard
router.put('/:id', authenticateToken, checkResourceOwnership('flashcard'), flashcardValidation, async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            russianWord,
            englishTranslations,
            ipaTranscription,
            exampleSentence,
            imageUrl,
            difficultyLevel,
            tags = []
        } = req.body;

        // Check if another card with the same word exists (excluding current card)
        const existingCard = await query(
            'SELECT card_id FROM flashcards WHERE user_id = $1 AND russian_word = $2 AND card_id != $3',
            [req.userId, russianWord, req.params.id]
        );

        if (existingCard.rows.length > 0) {
            return res.status(409).json({
                message: 'You already have another flashcard for this word'
            });
        }

        // Generate new audio if word changed
        let audioUrl = null;
        const currentCard = await query(
            'SELECT russian_word, audio_url FROM flashcards WHERE card_id = $1',
            [req.params.id]
        );

        if (currentCard.rows[0].russian_word !== russianWord) {
            try {
                audioUrl = await generateAudio(russianWord);
            } catch (audioError) {
                console.warn('Audio generation failed:', audioError.message);
                audioUrl = currentCard.rows[0].audio_url; // Keep existing audio
            }
        } else {
            audioUrl = currentCard.rows[0].audio_url; // Keep existing audio
        }

        // Update flashcard
        const result = await query(
            `UPDATE flashcards
            SET russian_word = $1, english_translations = $2, ipa_transcription = $3,
                audio_url = $4, example_sentence = $5, image_url = $6,
                difficulty_level = $7, tags = $8, updated_at = CURRENT_TIMESTAMP
            WHERE card_id = $9 AND user_id = $10
            RETURNING card_id, russian_word, english_translations, ipa_transcription,
                     audio_url, example_sentence, image_url, difficulty_level, tags, updated_at`,
            [russianWord, JSON.stringify(englishTranslations), ipaTranscription, audioUrl,
             exampleSentence, imageUrl, difficultyLevel, JSON.stringify(tags), req.params.id, req.userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({
                message: 'Flashcard not found'
            });
        }

        res.json({
            message: 'Flashcard updated successfully',
            flashcard: result.rows[0]
        });

    } catch (error) {
        console.error('Update flashcard error:', error);
        res.status(500).json({
            message: 'Internal server error while updating flashcard'
        });
    }
});

// Delete flashcard
router.delete('/:id', authenticateToken, checkResourceOwnership('flashcard'), async (req, res) => {
    try {
        await transaction(async (client) => {
            // Delete practice sessions first (foreign key constraint)
            await client.query(
                'DELETE FROM practice_sessions WHERE card_id = $1 AND user_id = $2',
                [req.params.id, req.userId]
            );

            // Delete flashcard
            const result = await client.query(
                'DELETE FROM flashcards WHERE card_id = $1 AND user_id = $2 RETURNING card_id',
                [req.params.id, req.userId]
            );

            if (result.rows.length === 0) {
                throw new Error('Flashcard not found');
            }
        });

        res.json({
            message: 'Flashcard deleted successfully'
        });

    } catch (error) {
        console.error('Delete flashcard error:', error);

        if (error.message === 'Flashcard not found') {
            return res.status(404).json({
                message: 'Flashcard not found'
            });
        }

        res.status(500).json({
            message: 'Internal server error while deleting flashcard'
        });
    }
});

// Bulk operations
router.post('/bulk-delete', authenticateToken, async (req, res) => {
    try {
        const { cardIds } = req.body;

        if (!Array.isArray(cardIds) || cardIds.length === 0) {
            return res.status(400).json({
                message: 'Card IDs array is required'
            });
        }

        await transaction(async (client) => {
            // Delete practice sessions
            await client.query(
                'DELETE FROM practice_sessions WHERE card_id = ANY($1) AND user_id = $2',
                [cardIds, req.userId]
            );

            // Delete flashcards
            const result = await client.query(
                'DELETE FROM flashcards WHERE card_id = ANY($1) AND user_id = $2 RETURNING card_id',
                [cardIds, req.userId]
            );

            return result.rows.length;
        });

        res.json({
            message: `Successfully deleted flashcards`
        });

    } catch (error) {
        console.error('Bulk delete error:', error);
        res.status(500).json({
            message: 'Internal server error during bulk delete'
        });
    }
});

module.exports = router;
