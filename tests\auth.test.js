const request = require('supertest');
const app = require('../server');
const { query } = require('../config/database');

describe('Authentication Endpoints', () => {
    let testUser = {
        email: '<EMAIL>',
        password: 'testpassword123',
        firstName: 'Test',
        lastName: 'User'
    };

    beforeEach(async () => {
        // Clean up test user before each test
        await query('DELETE FROM users WHERE email = $1', [testUser.email]);
    });

    afterAll(async () => {
        // Clean up after all tests
        await query('DELETE FROM users WHERE email = $1', [testUser.email]);
    });

    describe('POST /api/auth/register', () => {
        it('should register a new user successfully', async () => {
            const response = await request(app)
                .post('/api/auth/register')
                .send(testUser)
                .expect(201);

            expect(response.body).toHaveProperty('token');
            expect(response.body).toHaveProperty('user');
            expect(response.body.user.email).toBe(testUser.email);
            expect(response.body.user.firstName).toBe(testUser.firstName);
        });

        it('should reject registration with invalid email', async () => {
            const invalidUser = { ...testUser, email: 'invalid-email' };
            
            const response = await request(app)
                .post('/api/auth/register')
                .send(invalidUser)
                .expect(400);

            expect(response.body).toHaveProperty('message', 'Validation failed');
        });

        it('should reject registration with short password', async () => {
            const invalidUser = { ...testUser, password: '123' };
            
            const response = await request(app)
                .post('/api/auth/register')
                .send(invalidUser)
                .expect(400);

            expect(response.body).toHaveProperty('message', 'Validation failed');
        });

        it('should reject duplicate email registration', async () => {
            // Register user first time
            await request(app)
                .post('/api/auth/register')
                .send(testUser)
                .expect(201);

            // Try to register again with same email
            const response = await request(app)
                .post('/api/auth/register')
                .send(testUser)
                .expect(409);

            expect(response.body).toHaveProperty('message', 'User with this email already exists');
        });
    });

    describe('POST /api/auth/login', () => {
        beforeEach(async () => {
            // Register user before login tests
            await request(app)
                .post('/api/auth/register')
                .send(testUser);
        });

        it('should login with valid credentials', async () => {
            const response = await request(app)
                .post('/api/auth/login')
                .send({
                    email: testUser.email,
                    password: testUser.password
                })
                .expect(200);

            expect(response.body).toHaveProperty('token');
            expect(response.body).toHaveProperty('user');
            expect(response.body.user.email).toBe(testUser.email);
        });

        it('should reject login with invalid email', async () => {
            const response = await request(app)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: testUser.password
                })
                .expect(401);

            expect(response.body).toHaveProperty('message', 'Invalid email or password');
        });

        it('should reject login with invalid password', async () => {
            const response = await request(app)
                .post('/api/auth/login')
                .send({
                    email: testUser.email,
                    password: 'wrongpassword'
                })
                .expect(401);

            expect(response.body).toHaveProperty('message', 'Invalid email or password');
        });

        it('should reject login with missing fields', async () => {
            const response = await request(app)
                .post('/api/auth/login')
                .send({
                    email: testUser.email
                    // missing password
                })
                .expect(400);

            expect(response.body).toHaveProperty('message', 'Validation failed');
        });
    });

    describe('GET /api/auth/verify', () => {
        let authToken;

        beforeEach(async () => {
            // Register and get token
            const registerResponse = await request(app)
                .post('/api/auth/register')
                .send(testUser);
            
            authToken = registerResponse.body.token;
        });

        it('should verify valid token', async () => {
            const response = await request(app)
                .get('/api/auth/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).toHaveProperty('user');
            expect(response.body.user.email).toBe(testUser.email);
        });

        it('should reject request without token', async () => {
            const response = await request(app)
                .get('/api/auth/verify')
                .expect(401);

            expect(response.body).toHaveProperty('message', 'Access token is required');
        });

        it('should reject request with invalid token', async () => {
            const response = await request(app)
                .get('/api/auth/verify')
                .set('Authorization', 'Bearer invalid-token')
                .expect(401);

            expect(response.body).toHaveProperty('message', 'Invalid token');
        });
    });

    describe('POST /api/auth/logout', () => {
        let authToken;

        beforeEach(async () => {
            // Register and get token
            const registerResponse = await request(app)
                .post('/api/auth/register')
                .send(testUser);
            
            authToken = registerResponse.body.token;
        });

        it('should logout successfully with valid token', async () => {
            const response = await request(app)
                .post('/api/auth/logout')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).toHaveProperty('message', 'Logout successful');
        });

        it('should reject logout without token', async () => {
            const response = await request(app)
                .post('/api/auth/logout')
                .expect(401);

            expect(response.body).toHaveProperty('message', 'Access token is required');
        });
    });
});
