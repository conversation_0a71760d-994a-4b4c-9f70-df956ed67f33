const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

/**
 * Middleware to require admin role for protected routes
 * Must be used after authenticateToken middleware
 */
const requireAdmin = (req, res, next) => {
    try {
        // Check if user is authenticated
        if (!req.user) {
            return res.status(401).json({ 
                message: 'Authentication required' 
            });
        }

        // Check if user has admin role
        if (!req.user.isAdmin) {
            return res.status(403).json({ 
                message: 'Admin access required. This action requires administrator privileges.' 
            });
        }

        // User is admin, proceed to next middleware
        next();
    } catch (error) {
        console.error('Admin auth middleware error:', error);
        res.status(500).json({ 
            message: 'Internal server error during authorization' 
        });
    }
};

/**
 * Enhanced authentication middleware that includes admin role
 * Replaces the basic authenticateToken for admin routes
 */
const authenticateAdmin = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({ 
                message: 'Access token required' 
            });
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Get fresh user data from database to ensure admin status is current
        const result = await query(
            'SELECT user_id, email, is_admin, is_active FROM users WHERE user_id = $1',
            [decoded.userId]
        );

        if (result.rows.length === 0) {
            return res.status(401).json({ 
                message: 'User not found' 
            });
        }

        const user = result.rows[0];

        // Check if user account is active
        if (!user.is_active) {
            return res.status(401).json({ 
                message: 'Account is deactivated' 
            });
        }

        // Check if user is admin
        if (!user.is_admin) {
            return res.status(403).json({ 
                message: 'Admin access required' 
            });
        }

        // Attach user info to request
        req.user = {
            userId: user.user_id,
            email: user.email,
            isAdmin: user.is_admin,
            isActive: user.is_active
        };

        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ 
                message: 'Invalid access token' 
            });
        }
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({ 
                message: 'Access token expired' 
            });
        }

        console.error('Admin authentication error:', error);
        res.status(500).json({ 
            message: 'Internal server error during authentication' 
        });
    }
};

/**
 * Middleware to check if user is admin or owns the resource
 * Useful for routes where admins can access any resource, but users can only access their own
 */
const requireAdminOrOwner = (userIdParam = 'userId') => {
    return (req, res, next) => {
        try {
            if (!req.user) {
                return res.status(401).json({ 
                    message: 'Authentication required' 
                });
            }

            const resourceUserId = req.params[userIdParam] || req.body[userIdParam];
            
            // Admin can access any resource
            if (req.user.isAdmin) {
                return next();
            }

            // Regular user can only access their own resources
            if (req.user.userId.toString() === resourceUserId.toString()) {
                return next();
            }

            return res.status(403).json({ 
                message: 'Access denied. You can only access your own resources.' 
            });
        } catch (error) {
            console.error('Admin or owner auth middleware error:', error);
            res.status(500).json({ 
                message: 'Internal server error during authorization' 
            });
        }
    };
};

/**
 * Audit logging middleware for admin actions
 * Logs all admin actions for security and compliance
 */
const auditAdminAction = (action) => {
    return (req, res, next) => {
        // Store original res.json to intercept response
        const originalJson = res.json;
        
        res.json = function(data) {
            // Log admin action
            console.log(`[ADMIN AUDIT] ${new Date().toISOString()} - User: ${req.user.email} (${req.user.userId}) - Action: ${action} - Method: ${req.method} - Path: ${req.path} - IP: ${req.ip}`);
            
            // Call original res.json
            originalJson.call(this, data);
        };
        
        next();
    };
};

module.exports = {
    requireAdmin,
    authenticateAdmin,
    requireAdminOrOwner,
    auditAdminAction
};
