-- Rollback Migration: Remove flashcard fields added in 002_add_flashcard_fields.sql
-- Date: 2025-06-24

-- Drop indexes first
DROP INDEX IF EXISTS idx_flashcards_part_of_speech;
DROP INDEX IF EXISTS idx_flashcards_gender;
DROP INDEX IF EXISTS idx_flashcards_flashcard_sets;

-- Remove columns from flashcards table
ALTER TABLE flashcards 
DROP COLUMN IF EXISTS part_of_speech,
DROP COLUMN IF EXISTS number_type,
DROP COLUMN IF EXISTS gender,
DROP COLUMN IF EXISTS flashcard_sets,
DROP COLUMN IF EXISTS author_name,
DROP COLUMN IF EXISTS author_link;

-- Remove migration tracking
DELETE FROM schema_migrations WHERE migration_name = '002_add_flashcard_fields.sql';
