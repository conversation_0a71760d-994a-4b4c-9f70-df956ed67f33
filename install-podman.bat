@echo off
echo ============================================
echo Podman Installation Helper
echo ============================================
echo.

REM Check if <PERSON><PERSON> is already installed
podman --version >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] <PERSON><PERSON> is already installed!
    podman --version
    echo.
    echo Checking Podman machine status...
    podman machine list
    echo.
    echo You can now run: podman-start.bat
    pause
    exit /b 0
)

echo Podman is not installed. Let's install it!
echo.

REM Try winget first
echo Attempting installation with winget...
winget install RedHat.Podman-Desktop --accept-source-agreements --accept-package-agreements

if %errorlevel% == 0 (
    echo.
    echo [✓] Podman Desktop installed successfully with winget!
    goto :setup
)

echo.
echo winget installation failed. Trying alternative methods...
echo.

REM Check if Chocolatey is available
choco --version >nul 2>&1
if %errorlevel% == 0 (
    echo Attempting installation with Chocolate<PERSON>...
    choco install podman-desktop -y
    
    if %errorlevel% == 0 (
        echo.
        echo [✓] Podman Desktop installed successfully with Chocolatey!
        goto :setup
    )
)

REM Manual installation instructions
echo.
echo ============================================
echo Manual Installation Required
echo ============================================
echo.
echo Automatic installation failed. Please install manually:
echo.
echo Option 1 - Podman Desktop (Recommended):
echo 1. Go to: https://podman-desktop.io/
echo 2. Click "Download for Windows"
echo 3. Run the installer with default settings
echo 4. Start Podman Desktop after installation
echo.
echo Option 2 - Command Line Podman:
echo 1. Go to: https://github.com/containers/podman/releases
echo 2. Download the latest Windows .msi installer
echo 3. Run the installer
echo 4. Add Podman to your PATH
echo.
echo After installation, run this script again to verify.
echo.
pause
exit /b 1

:setup
echo.
echo ============================================
echo Post-Installation Setup
echo ============================================
echo.

REM Wait for installation to complete
echo Waiting for installation to complete...
timeout /t 10 /nobreak >nul

REM Refresh PATH
echo Refreshing environment variables...
call refreshenv >nul 2>&1

REM Check if Podman is now available
podman --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo [WARNING] Podman command not found in PATH.
    echo Please restart your terminal or computer and try again.
    echo.
    pause
    exit /b 1
)

echo [✓] Podman is now available!
podman --version

echo.
echo Initializing Podman machine...
podman machine init

if %errorlevel% == 0 (
    echo [✓] Podman machine initialized successfully!
    
    echo.
    echo Starting Podman machine...
    podman machine start
    
    if %errorlevel% == 0 (
        echo [✓] Podman machine started successfully!
        
        echo.
        echo Testing Podman with hello-world...
        podman run hello-world
        
        if %errorlevel% == 0 (
            echo.
            echo ============================================
            echo 🎉 Podman Installation Complete!
            echo ============================================
            echo.
            echo Next steps:
            echo 1. Run: podman-start.bat
            echo 2. Wait for containers to start
            echo 3. Open: http://localhost:3000
            echo.
            echo Useful commands:
            echo - podman ps          (list containers)
            echo - podman images      (list images)
            echo - podman machine list (list machines)
            echo.
        else
            echo.
            echo [WARNING] Podman test failed. Please check the setup.
        fi
    else
        echo.
        echo [ERROR] Failed to start Podman machine.
        echo Please try manually: podman machine start
    fi
else
    echo.
    echo [ERROR] Failed to initialize Podman machine.
    echo Please try manually: podman machine init
fi

echo.
echo Installation process complete!
echo Press any key to exit...
pause >nul
