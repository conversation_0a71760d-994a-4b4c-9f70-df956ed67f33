# 📋 Russian Flashcard Platform - Admin-Centric Refactoring Task Plan

## 🎯 Project Overview

**Objective**: Transform the current user-driven flashcard system into an admin-centric platform following the Grok prompt specifications.

**Current State**: Users can create flashcards directly and practice them with spaced repetition.

**Target State**: Admin-only card creation with user word requests, curated practice sets, and personal overlays.

---

## 🏗️ Architecture Changes

### **Before (Current)**
- Users create flashcards directly
- Users practice their own cards
- Simple spaced repetition system

### **After (Target)**
- **Users**: Request words → Practice admin sets → Add personal overlays
- **Admins**: Review requests → Create cards → Curate sets → Manage users

---

## 📊 Task Phases

### **Phase 1: Database Schema Refactoring** [IN PROGRESS]
**Goal**: Update database to support new admin-centric architecture

#### Tasks:
- [ ] **Create word_requests table** - User word requests with status tracking (pending/approved/rejected)
- [ ] **Create user_card_overlays table** - User-specific notes and examples on cards
- [ ] **Create practice_sets table** - Admin-curated practice sets with card assignments
- [ ] **Update flashcards table structure** - Ensure matches prompt requirements (UUID, required audio_url, etc.)
- [ ] **Create database migration scripts** - Transform existing data to new schema

#### Database Schema:
```sql
-- Word Requests Table
CREATE TABLE word_requests (
    request_id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(user_id),
    russian_word VARCHAR(100) NOT NULL,
    comments TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User Card Overlays Table
CREATE TABLE user_card_overlays (
    overlay_id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(user_id),
    card_id UUID REFERENCES flashcards(card_id),
    notes TEXT,
    user_examples TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, card_id)
);

-- Practice Sets Table
CREATE TABLE practice_sets (
    set_id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    card_ids JSONB NOT NULL,
    criteria TEXT,
    assigned_user_ids JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

### **Phase 2: Authentication & Authorization Enhancement**
**Goal**: Implement role-based access control

#### Tasks:
- [ ] **Add is_admin column to users table** - Alter users table to include is_admin boolean field with default false
- [ ] **Create admin role middleware** - Implement middleware to check admin role for protected routes
- [ ] **Update JWT token to include role** - Modify JWT payload to include user role information
- [ ] **Create admin route protection** - Protect all /api/admin/* routes with admin role verification
- [ ] **Add admin user seeding** - Create script to seed initial admin user for testing

#### Implementation Notes:
- JWT payload: `{ userId, email, isAdmin }`
- Admin middleware: Check `req.user.isAdmin === true`
- Protected routes: All `/api/admin/*` endpoints

---

### **Phase 3: Word Request System Implementation**
**Goal**: Replace user card creation with request system

#### Tasks:
- [ ] **Create word request API endpoints** - Build POST /api/requests for users to submit word requests
- [ ] **Remove user flashcard creation** - Remove/disable direct flashcard creation endpoints for regular users
- [ ] **Create request status tracking** - Add endpoints for users to view their request status and history
- [ ] **Add request validation** - Implement validation for word requests (duplicate checking, format validation)

#### API Endpoints:
- `POST /api/requests` - Submit word request
- `GET /api/requests` - Get user's request history
- `GET /api/requests/:id` - Get specific request status

---

### **Phase 4: Admin Interface Development**
**Goal**: Build comprehensive admin dashboard

#### Tasks:
- [ ] **Create admin dashboard API** - Build GET /admin/dashboard with pending requests and user metrics
- [ ] **Create admin card management API** - Build CRUD endpoints for admin flashcard management (/api/admin/cards)
- [ ] **Create admin request management** - Build endpoints to approve/reject word requests (/api/admin/requests)
- [ ] **Create admin user management** - Build endpoints for admin to manage users (/api/admin/users)
- [ ] **Build admin dashboard UI** - Create React components for admin dashboard interface
- [ ] **Build admin card management UI** - Create React components for admin card CRUD interface

#### Admin API Endpoints:
- `GET /api/admin/dashboard` - Dashboard metrics
- `GET/POST/PUT/DELETE /api/admin/cards` - Card management
- `POST /api/admin/requests/:id/approve` - Approve request
- `POST /api/admin/requests/:id/reject` - Reject request
- `GET/PUT/DELETE /api/admin/users` - User management

---

### **Phase 5: Practice Sets Management**
**Goal**: Implement admin-curated practice sets

#### Tasks:
- [ ] **Create practice sets API** - Build CRUD endpoints for admin practice set management (/api/admin/sets)
- [ ] **Create set assignment system** - Implement system for admins to assign sets to specific users or all users
- [ ] **Update practice session logic** - Modify practice sessions to use admin-curated sets instead of user cards
- [ ] **Create set selection UI** - Build interface for users to select from assigned practice sets
- [ ] **Build admin set management UI** - Create React components for admin to create and manage practice sets

#### Set Features:
- 12 cards per set (as per prompt)
- Tag-based criteria (e.g., "food", "accusative")
- User assignment (specific users or all users)
- Set difficulty levels

---

### **Phase 6: User Overlay System**
**Goal**: Add personal notes/examples overlay system

#### Tasks:
- [ ] **Create overlay API endpoints** - Build CRUD endpoints for user card overlays (/api/overlays)
- [ ] **Create overlay UI components** - Build React components for users to add/edit personal notes and examples
- [ ] **Integrate overlays in practice** - Display user overlays during practice sessions
- [ ] **Add overlay editor in cards** - Allow users to edit overlays when viewing individual cards

#### Overlay Features:
- Personal notes on any card
- User-specific examples
- Private to each user
- Displayed during practice

---

### **Phase 7: Frontend Refactoring**
**Goal**: Update user interface for new workflow

#### Tasks:
- [ ] **Remove create card page** - Remove or replace user card creation interface with word request form
- [ ] **Create word request form** - Build new interface for users to request words instead of creating cards
- [ ] **Update dashboard for new workflow** - Modify user dashboard to show assigned sets and request status
- [ ] **Add admin route protection UI** - Implement frontend route protection for admin-only pages
- [ ] **Create admin navigation** - Add admin-specific navigation and menu items
- [ ] **Update practice flow UI** - Modify practice interface to work with sets and overlays

#### UI Changes:
- Replace "Create Card" with "Request Word"
- Add admin menu for admin users
- Show assigned sets on dashboard
- Display overlays in practice

---

### **Phase 8: Testing & Documentation**
**Goal**: Ensure quality and maintainability

#### Tasks:
- [ ] **Create API integration tests** - Write comprehensive tests for all new API endpoints
- [ ] **Create admin workflow tests** - Test complete admin workflow from request approval to set creation
- [ ] **Create user workflow tests** - Test complete user workflow from word request to practice sessions
- [ ] **Update documentation** - Update README and API documentation for new architecture
- [ ] **Create deployment guide** - Document deployment process for new admin-centric system

---

## 🔄 New User Workflows

### **User Workflow**
1. **Register** → Regular user account
2. **Request Words** → Submit Russian words via form
3. **Wait for Approval** → Admin reviews and creates cards
4. **Select Practice Set** → Choose from assigned sets
5. **Practice with Overlays** → Add personal notes during practice
6. **Track Progress** → View request status and practice stats

### **Admin Workflow**
1. **Admin Dashboard** → View pending requests and metrics
2. **Review Requests** → Approve/reject user word requests
3. **Create Cards** → Build flashcards with audio and translations
4. **Curate Sets** → Create practice sets (12 cards each)
5. **Assign Sets** → Assign to specific users or all users
6. **Manage Users** → User administration, promote to admin

---

## 📝 Implementation Notes

### **Data Migration Strategy**
1. Backup existing data
2. Add new columns with defaults
3. Create new tables
4. Migrate existing flashcards to admin-created
5. Create default practice sets from existing cards

### **Backward Compatibility**
- Existing user cards become admin cards
- Current practice sessions continue working
- Gradual migration to new workflow

### **Security Considerations**
- Admin role verification on all admin endpoints
- Input validation for all new forms
- Rate limiting on word requests
- Audit logging for admin actions

---

## 🚀 Getting Started

**Current Status**: Phase 1 in progress
**Next Steps**: 
1. Complete database schema updates
2. Add admin role to users table
3. Create migration scripts
4. Test database changes

**Estimated Timeline**: 8-10 weeks for complete refactoring

---

*This document serves as the master task plan for transforming the Russian Flashcard Platform into an admin-centric system following the Grok prompt specifications.*
