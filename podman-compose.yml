version: '3.8'

services:
  # PostgreSQL Database for Development
  database:
    image: docker.io/postgres:15-alpine
    container_name: russian-flashcards-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: russian_flashcards_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    ports:
      - "5432:5432"
    networks:
      - flashcards-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev_user -d russian_flashcards_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: docker.io/redis:7-alpine
    container_name: russian-flashcards-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - flashcards-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Russian Flashcards Application
  app:
    build:
      context: .
      dockerfile: Containerfile
    container_name: russian-flashcards-app
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      
      # Database Configuration
      DB_HOST: database
      DB_PORT: 5432
      DB_NAME: russian_flashcards_dev
      DB_USER: dev_user
      DB_PASSWORD: dev_password
      DB_SSL: false
      
      # JWT Configuration
      JWT_SECRET: podman_dev_jwt_secret_change_in_production
      JWT_EXPIRES_IN: 24h
      
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # API Keys (set these in .env file)
      GOOGLE_TTS_API_KEY: ${GOOGLE_TTS_API_KEY:-}
      FORVO_API_KEY: ${FORVO_API_KEY:-}
      YANDEX_DICTIONARY_API_KEY: ${YANDEX_DICTIONARY_API_KEY:-}
      
      # Development settings
      DEBUG: true
      LOG_LEVEL: debug
      
    ports:
      - "3000:3000"
    volumes:
      - .:/app:Z
      - /app/node_modules
      - ./uploads:/app/uploads:Z
    networks:
      - flashcards-network
    depends_on:
      - database
      - redis
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  flashcards-network:
