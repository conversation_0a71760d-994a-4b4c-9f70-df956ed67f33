#!/bin/bash

echo "Russian Flashcards Setup Script"
echo "================================"

echo ""
echo "1. Installing dependencies..."
npm install

echo ""
echo "2. Setting up environment..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Created .env file from template"
    echo "Please edit .env with your configuration"
else
    echo ".env file already exists"
fi

echo ""
echo "3. Database setup instructions:"
echo "- Create PostgreSQL database: russian_flashcards"
echo "- Run: psql -d russian_flashcards -f database/schema.sql"
echo "- Update .env with your database credentials"

echo ""
echo "4. Starting the application..."
echo "Run: npm start (production) or npm run dev (development)"

echo ""
echo "Setup complete! Please configure your .env file and database."
