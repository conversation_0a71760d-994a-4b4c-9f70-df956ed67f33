const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation rules for word requests
const wordRequestValidation = [
    body('russianWord')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Russian word is required and must be 1-100 characters')
        .matches(/[\u0400-\u04FF]/)
        .withMessage('Russian word must contain Cyrillic characters'),
    body('comments')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Comments must be 500 characters or less')
];

// Submit a new word request
router.post('/', wordRequestValidation, async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { russianWord, comments } = req.body;
        const userId = req.user.userId;

        // Check if user already requested this word
        const existingRequest = await query(
            'SELECT request_id, status FROM word_requests WHERE user_id = $1 AND russian_word = $2',
            [userId, russianWord]
        );

        if (existingRequest.rows.length > 0) {
            const existing = existingRequest.rows[0];
            return res.status(409).json({
                message: 'You have already requested this word',
                existingRequest: {
                    requestId: existing.request_id,
                    status: existing.status
                }
            });
        }

        // Check if word already exists in flashcards
        const existingCard = await query(
            'SELECT card_id FROM flashcards WHERE russian_word = $1',
            [russianWord]
        );

        if (existingCard.rows.length > 0) {
            return res.status(409).json({
                message: 'This word already exists in the flashcard database',
                cardId: existingCard.rows[0].card_id
            });
        }

        // Create word request
        const result = await query(`
            INSERT INTO word_requests (user_id, russian_word, comments)
            VALUES ($1, $2, $3)
            RETURNING request_id, russian_word, comments, status, submitted_at
        `, [userId, russianWord, comments || null]);

        const newRequest = result.rows[0];

        res.status(201).json({
            message: 'Word request submitted successfully',
            request: {
                requestId: newRequest.request_id,
                russianWord: newRequest.russian_word,
                comments: newRequest.comments,
                status: newRequest.status,
                submittedAt: newRequest.submitted_at
            }
        });

    } catch (error) {
        console.error('Word request submission error:', error);
        res.status(500).json({
            message: 'Internal server error while submitting request'
        });
    }
});

// Get user's word requests
router.get('/', async (req, res) => {
    try {
        const userId = req.user.userId;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const status = req.query.status || 'all';
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE user_id = $1';
        let params = [userId, limit, offset];
        
        if (status !== 'all') {
            whereClause += ' AND status = $4';
            params.push(status);
        }

        const requestsResult = await query(`
            SELECT 
                request_id,
                russian_word,
                comments,
                status,
                admin_notes,
                submitted_at,
                processed_at
            FROM word_requests
            ${whereClause}
            ORDER BY submitted_at DESC
            LIMIT $2 OFFSET $3
        `, params);

        // Get total count for pagination
        let countParams = [userId];
        let countWhere = 'WHERE user_id = $1';
        
        if (status !== 'all') {
            countWhere += ' AND status = $2';
            countParams.push(status);
        }

        const countResult = await query(
            `SELECT COUNT(*) as total FROM word_requests ${countWhere}`,
            countParams
        );

        res.json({
            requests: requestsResult.rows,
            pagination: {
                page,
                limit,
                total: parseInt(countResult.rows[0].total),
                totalPages: Math.ceil(countResult.rows[0].total / limit)
            }
        });

    } catch (error) {
        console.error('Get word requests error:', error);
        res.status(500).json({
            message: 'Internal server error while loading requests'
        });
    }
});

// Get specific word request
router.get('/:id', async (req, res) => {
    try {
        const requestId = req.params.id;
        const userId = req.user.userId;

        const result = await query(`
            SELECT 
                request_id,
                russian_word,
                comments,
                status,
                admin_notes,
                submitted_at,
                processed_at
            FROM word_requests
            WHERE request_id = $1 AND user_id = $2
        `, [requestId, userId]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                message: 'Request not found'
            });
        }

        res.json({
            request: result.rows[0]
        });

    } catch (error) {
        console.error('Get word request error:', error);
        res.status(500).json({
            message: 'Internal server error while loading request'
        });
    }
});

// Cancel pending word request
router.delete('/:id', async (req, res) => {
    try {
        const requestId = req.params.id;
        const userId = req.user.userId;

        const result = await query(`
            DELETE FROM word_requests
            WHERE request_id = $1 AND user_id = $2 AND status = 'pending'
            RETURNING russian_word
        `, [requestId, userId]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                message: 'Request not found or cannot be cancelled (only pending requests can be cancelled)'
            });
        }

        res.json({
            message: 'Request cancelled successfully',
            word: result.rows[0].russian_word
        });

    } catch (error) {
        console.error('Cancel word request error:', error);
        res.status(500).json({
            message: 'Internal server error while cancelling request'
        });
    }
});

// Get request statistics for user
router.get('/stats/summary', async (req, res) => {
    try {
        const userId = req.user.userId;

        const result = await query(`
            SELECT 
                status,
                COUNT(*) as count
            FROM word_requests
            WHERE user_id = $1
            GROUP BY status
        `, [userId]);

        const stats = {
            pending: 0,
            approved: 0,
            rejected: 0,
            total: 0
        };

        result.rows.forEach(row => {
            stats[row.status] = parseInt(row.count);
            stats.total += parseInt(row.count);
        });

        res.json({ stats });

    } catch (error) {
        console.error('Get request stats error:', error);
        res.status(500).json({
            message: 'Internal server error while loading statistics'
        });
    }
});

module.exports = router;
