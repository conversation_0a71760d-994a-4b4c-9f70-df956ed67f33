@echo off
echo ============================================
echo Russian Flashcards - Tool Installation
echo ============================================
echo.

echo This script will help you install the required tools:
echo - Git for Windows
echo - Node.js and npm
echo - PostgreSQL
echo.

echo Checking current installations...
echo.

REM Check if Git is installed
git --version >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] Git is already installed
    git --version
) else (
    echo [✗] Git is not installed
    set NEED_GIT=1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] Node.js is already installed
    node --version
    npm --version
) else (
    echo [✗] Node.js is not installed
    set NEED_NODE=1
)

REM Check if PostgreSQL is installed
psql --version >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] PostgreSQL is already installed
    psql --version
) else (
    echo [✗] PostgreSQL is not installed
    set NEED_POSTGRES=1
)

echo.
echo ============================================

if defined NEED_GIT (
    echo.
    echo Installing Git for Windows...
    echo Downloading Git installer...
    
    powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/git-for-windows/git/releases/download/v2.47.1.windows.1/Git-2.47.1-64-bit.exe' -OutFile 'git-installer.exe'}"
    
    if exist git-installer.exe (
        echo Running Git installer...
        echo Please follow the installation wizard with default settings.
        start /wait git-installer.exe
        del git-installer.exe
        echo Git installation completed.
    ) else (
        echo Failed to download Git installer.
        echo Please download manually from: https://git-scm.com/download/win
    )
)

if defined NEED_NODE (
    echo.
    echo Installing Node.js...
    echo Downloading Node.js installer...
    
    powershell -Command "& {Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.18.0/node-v20.18.0-x64.msi' -OutFile 'node-installer.msi'}"
    
    if exist node-installer.msi (
        echo Running Node.js installer...
        echo Please follow the installation wizard with default settings.
        start /wait msiexec /i node-installer.msi /quiet
        del node-installer.msi
        echo Node.js installation completed.
    ) else (
        echo Failed to download Node.js installer.
        echo Please download manually from: https://nodejs.org/
    )
)

if defined NEED_POSTGRES (
    echo.
    echo PostgreSQL installation requires manual setup.
    echo Please download and install from: https://www.postgresql.org/download/windows/
    echo.
    echo Installation notes:
    echo - Use port 5432 (default)
    echo - Set a secure superuser password
    echo - Remember your password for database setup
    echo.
    pause
)

echo.
echo ============================================
echo Installation Summary
echo ============================================

echo.
echo Verifying installations...

git --version >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] Git: 
    git --version
) else (
    echo [✗] Git: Not found - please restart terminal or reinstall
)

node --version >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] Node.js: 
    node --version
    echo [✓] npm: 
    npm --version
) else (
    echo [✗] Node.js: Not found - please restart terminal or reinstall
)

psql --version >nul 2>&1
if %errorlevel% == 0 (
    echo [✓] PostgreSQL: 
    psql --version
) else (
    echo [✗] PostgreSQL: Not found - please install manually
)

echo.
echo ============================================
echo Next Steps
echo ============================================
echo.

if exist package.json (
    echo 1. Install project dependencies:
    echo    npm install
    echo.
    echo 2. Set up environment:
    echo    copy .env.example .env
    echo    ^(then edit .env with your settings^)
    echo.
    echo 3. Set up database:
    echo    createdb russian_flashcards
    echo    psql -U postgres -d russian_flashcards -f database/schema.sql
    echo.
    echo 4. Start the application:
    echo    npm run dev
) else (
    echo 1. Initialize git repository:
    echo    git init
    echo.
    echo 2. Follow the setup instructions in README.md
)

echo.
echo For detailed instructions, see INSTALLATION_GUIDE.md
echo.

pause
