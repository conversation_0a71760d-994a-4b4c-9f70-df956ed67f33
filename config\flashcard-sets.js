// Predefined flashcard sets based on your existing interface
const FLASHCARD_SETS = [
    // Frequency-based sets
    "100 Most Frequent Russian Adjectives",
    "100 Most Frequent Russian Nouns", 
    "100 Most Frequent Russian Verbs",
    "300 Frequent Russian Words",
    
    // Advanced vocabulary
    "Advanced Names for Colors",
    "Advanced Verbs for Motion",
    
    // Age and life stages
    "Age-related Vocabulary - 2",
    
    // Grammar categories
    "Antonymic Adverbs",
    
    // Subject areas
    "Art Supplies",
    "Art Vocabulary",
    "Beginning",
    "Booze Related Words in Russian",
    "Business Vocabulary",
    "Chores",
    "Clothes",
    "Clothes Items",
    "Common Irregular Comparatives",
    "Comparatives",
    "Cooking Vocabulary",
    "Cookware",
    "Coronavirus Epidemic Vocabulary",
    "Covid Symptoms",
    "Crime and Punishment - Medicine Criminal Drugs",
    
    // Additional categories (commonly used in language learning)
    "Animals",
    "Body Parts",
    "Colors",
    "Days of the Week",
    "Emotions",
    "Family Members",
    "Food and Drinks",
    "Furniture",
    "Geography",
    "Health and Medicine",
    "Hobbies",
    "House and Home",
    "Jobs and Professions",
    "Money and Banking",
    "Months of the Year",
    "Music",
    "Nature",
    "Numbers",
    "School and Education",
    "Shopping",
    "Sports",
    "Technology",
    "Time",
    "Transportation",
    "Travel",
    "Weather",
    
    // Grammar-specific sets
    "Prepositions",
    "Conjunctions",
    "Pronouns",
    "Question Words",
    "Reflexive Verbs",
    "Irregular Verbs",
    "Perfective Verbs",
    "Imperfective Verbs",
    
    // Level-based sets
    "Beginner Vocabulary",
    "Intermediate Vocabulary", 
    "Advanced Vocabulary",
    "Expert Level Words"
];

// Part of speech options
const PARTS_OF_SPEECH = [
    "noun",
    "verb", 
    "adjective",
    "adverb",
    "pronoun",
    "preposition",
    "conjunction",
    "interjection",
    "numeral",
    "particle"
];

// Number types (for Russian grammar)
const NUMBER_TYPES = [
    "singular",
    "plural",
    "both",
    "uncountable"
];

// Gender options (for Russian nouns)
const GENDERS = [
    "masculine",
    "feminine", 
    "neuter",
    "common"
];

module.exports = {
    FLASHCARD_SETS,
    PARTS_OF_SPEECH,
    NUMBER_TYPES,
    GENDERS
};
