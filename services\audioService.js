const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

/**
 * Audio service for generating Russian pronunciation using Google Text-to-Speech
 */

// Ensure uploads directory exists
const UPLOAD_DIR = path.join(__dirname, '..', 'uploads', 'audio');

async function ensureUploadDir() {
    try {
        await fs.access(UPLOAD_DIR);
    } catch (error) {
        await fs.mkdir(UPLOAD_DIR, { recursive: true });
    }
}

/**
 * Generate audio using Google Text-to-Speech API
 * @param {string} text - Russian text to convert to speech
 * @returns {string} URL to the generated audio file
 */
async function generateAudioWithGoogle(text) {
    const apiKey = process.env.GOOGLE_TTS_API_KEY;
    
    if (!apiKey) {
        throw new Error('Google TTS API key not configured');
    }

    try {
        const response = await axios.post(
            `https://texttospeech.googleapis.com/v1/text:synthesize?key=${apiKey}`,
            {
                input: { text },
                voice: {
                    languageCode: 'ru-RU',
                    name: 'ru-RU-Wavenet-A', // Female voice
                    ssmlGender: 'FEMALE'
                },
                audioConfig: {
                    audioEncoding: 'MP3',
                    speakingRate: 0.9, // Slightly slower for learning
                    pitch: 0.0,
                    volumeGainDb: 0.0
                }
            },
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );

        if (!response.data.audioContent) {
            throw new Error('No audio content received from Google TTS');
        }

        // Save audio file
        await ensureUploadDir();
        const filename = `${Date.now()}_${text.replace(/[^a-zA-Zа-яё]/g, '_')}.mp3`;
        const filepath = path.join(UPLOAD_DIR, filename);
        
        // Decode base64 audio content
        const audioBuffer = Buffer.from(response.data.audioContent, 'base64');
        await fs.writeFile(filepath, audioBuffer);

        // Return relative URL
        return `/uploads/audio/${filename}`;

    } catch (error) {
        console.error('Google TTS error:', error.response?.data || error.message);
        throw new Error('Failed to generate audio with Google TTS');
    }
}

/**
 * Generate audio using Forvo API (alternative)
 * @param {string} word - Russian word to get pronunciation for
 * @returns {string} URL to the audio file
 */
async function generateAudioWithForvo(word) {
    const apiKey = process.env.FORVO_API_KEY;
    
    if (!apiKey) {
        throw new Error('Forvo API key not configured');
    }

    try {
        // Search for pronunciations
        const searchResponse = await axios.get(
            `https://apifree.forvo.com/key/${apiKey}/format/json/action/word-pronunciations/word/${encodeURIComponent(word)}/language/ru`
        );

        const pronunciations = searchResponse.data.items;
        
        if (!pronunciations || pronunciations.length === 0) {
            throw new Error('No pronunciations found on Forvo');
        }

        // Get the first (usually highest rated) pronunciation
        const bestPronunciation = pronunciations[0];
        const audioUrl = bestPronunciation.pathmp3;

        if (!audioUrl) {
            throw new Error('No audio URL found in Forvo response');
        }

        // Download and save the audio file
        const audioResponse = await axios.get(audioUrl, {
            responseType: 'arraybuffer'
        });

        await ensureUploadDir();
        const filename = `forvo_${Date.now()}_${word.replace(/[^a-zA-Zа-яё]/g, '_')}.mp3`;
        const filepath = path.join(UPLOAD_DIR, filename);
        
        await fs.writeFile(filepath, audioResponse.data);

        // Return relative URL
        return `/uploads/audio/${filename}`;

    } catch (error) {
        console.error('Forvo API error:', error.response?.data || error.message);
        throw new Error('Failed to generate audio with Forvo');
    }
}

/**
 * Main function to generate audio (tries Google TTS first, then Forvo)
 * @param {string} text - Russian text/word to convert to speech
 * @returns {string} URL to the generated audio file
 */
async function generateAudio(text) {
    if (!text || typeof text !== 'string') {
        throw new Error('Invalid text provided for audio generation');
    }

    // Clean the text
    const cleanText = text.trim();
    
    if (cleanText.length === 0) {
        throw new Error('Empty text provided for audio generation');
    }

    // Try Google TTS first
    if (process.env.GOOGLE_TTS_API_KEY) {
        try {
            return await generateAudioWithGoogle(cleanText);
        } catch (error) {
            console.warn('Google TTS failed, trying Forvo:', error.message);
        }
    }

    // Fallback to Forvo for single words
    if (process.env.FORVO_API_KEY && !cleanText.includes(' ')) {
        try {
            return await generateAudioWithForvo(cleanText);
        } catch (error) {
            console.warn('Forvo also failed:', error.message);
        }
    }

    throw new Error('All audio generation services failed');
}

/**
 * Get audio file info
 * @param {string} audioUrl - URL to the audio file
 * @returns {Object} Audio file information
 */
async function getAudioInfo(audioUrl) {
    try {
        if (!audioUrl || !audioUrl.startsWith('/uploads/audio/')) {
            throw new Error('Invalid audio URL');
        }

        const filename = path.basename(audioUrl);
        const filepath = path.join(UPLOAD_DIR, filename);
        
        const stats = await fs.stat(filepath);
        
        return {
            exists: true,
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
        };

    } catch (error) {
        return {
            exists: false,
            error: error.message
        };
    }
}

/**
 * Delete audio file
 * @param {string} audioUrl - URL to the audio file to delete
 * @returns {boolean} Success status
 */
async function deleteAudio(audioUrl) {
    try {
        if (!audioUrl || !audioUrl.startsWith('/uploads/audio/')) {
            return false;
        }

        const filename = path.basename(audioUrl);
        const filepath = path.join(UPLOAD_DIR, filename);
        
        await fs.unlink(filepath);
        return true;

    } catch (error) {
        console.error('Error deleting audio file:', error);
        return false;
    }
}

/**
 * Clean up old audio files (older than 30 days)
 */
async function cleanupOldAudioFiles() {
    try {
        await ensureUploadDir();
        const files = await fs.readdir(UPLOAD_DIR);
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        for (const file of files) {
            const filepath = path.join(UPLOAD_DIR, file);
            const stats = await fs.stat(filepath);
            
            if (stats.birthtime < thirtyDaysAgo) {
                await fs.unlink(filepath);
                console.log(`Deleted old audio file: ${file}`);
            }
        }

    } catch (error) {
        console.error('Error during audio cleanup:', error);
    }
}

module.exports = {
    generateAudio,
    generateAudioWithGoogle,
    generateAudioWithForvo,
    getAudioInfo,
    deleteAudio,
    cleanupOldAudioFiles
};
