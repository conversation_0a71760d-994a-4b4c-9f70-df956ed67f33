-- Migration: Add new fields to flashcards table to match existing interface
-- Date: 2025-06-24
-- Description: Add part_of_speech, number_type, gender, flashcard_sets, author_name, author_link

-- Add new columns to flashcards table
ALTER TABLE flashcards 
ADD COLUMN part_of_speech VARCHAR(50),
ADD COLUMN number_type VARCHAR(20),
ADD COLUMN gender VARCHAR(20),
ADD COLUMN flashcard_sets JSONB DEFAULT '[]',
ADD COLUMN author_name VARCHAR(255),
ADD COLUMN author_link VARCHAR(500);

-- Create indexes for better performance
CREATE INDEX idx_flashcards_part_of_speech ON flashcards(part_of_speech);
CREATE INDEX idx_flashcards_gender ON flashcards(gender);
CREATE INDEX idx_flashcards_flashcard_sets ON flashcards USING GIN(flashcard_sets);

-- Update migration tracking
INSERT INTO schema_migrations (migration_name, executed_at) 
VALUES ('002_add_flashcard_fields.sql', CURRENT_TIMESTAMP);
