const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database connection
const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'russian_flashcards',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
});

// Create migrations table if it doesn't exist
async function createMigrationsTable() {
    const query = `
        CREATE TABLE IF NOT EXISTS migrations (
            id SERIAL PRIMARY KEY,
            filename VARCHAR(255) NOT NULL UNIQUE,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
    `;
    await pool.query(query);
}

// Check if migration has been executed
async function isMigrationExecuted(filename) {
    const result = await pool.query(
        'SELECT 1 FROM migrations WHERE filename = $1',
        [filename]
    );
    return result.rows.length > 0;
}

// Record migration execution
async function recordMigration(filename) {
    await pool.query(
        'INSERT INTO migrations (filename) VALUES ($1)',
        [filename]
    );
}

// Execute migration file
async function executeMigration(filename) {
    const filePath = path.join(__dirname, 'migrations', filename);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    console.log(`Executing migration: ${filename}`);
    
    try {
        await pool.query(sql);
        await recordMigration(filename);
        console.log(`✅ Migration ${filename} completed successfully`);
    } catch (error) {
        console.error(`❌ Migration ${filename} failed:`, error.message);
        throw error;
    }
}

// Run all pending migrations
async function runMigrations() {
    try {
        await createMigrationsTable();
        
        const migrationsDir = path.join(__dirname, 'migrations');
        const files = fs.readdirSync(migrationsDir)
            .filter(file => file.endsWith('.sql') && !file.includes('rollback'))
            .sort();
        
        console.log('🚀 Starting database migrations...');
        
        for (const file of files) {
            if (!(await isMigrationExecuted(file))) {
                await executeMigration(file);
            } else {
                console.log(`⏭️  Migration ${file} already executed, skipping`);
            }
        }
        
        console.log('✅ All migrations completed successfully');
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// Run rollback for specific migration
async function rollbackMigration(migrationName) {
    try {
        const rollbackFile = `${migrationName.replace('.sql', '')}_rollback.sql`;
        const filePath = path.join(__dirname, 'migrations', rollbackFile);
        
        if (!fs.existsSync(filePath)) {
            throw new Error(`Rollback file not found: ${rollbackFile}`);
        }
        
        const sql = fs.readFileSync(filePath, 'utf8');
        
        console.log(`Rolling back migration: ${migrationName}`);
        
        await pool.query(sql);
        await pool.query('DELETE FROM migrations WHERE filename = $1', [migrationName]);
        
        console.log(`✅ Rollback completed for ${migrationName}`);
    } catch (error) {
        console.error('❌ Rollback failed:', error);
        throw error;
    } finally {
        await pool.end();
    }
}

// Command line interface
const command = process.argv[2];
const migrationName = process.argv[3];

switch (command) {
    case 'up':
        runMigrations();
        break;
    case 'rollback':
        if (!migrationName) {
            console.error('Please specify migration name for rollback');
            process.exit(1);
        }
        rollbackMigration(migrationName);
        break;
    default:
        console.log('Usage:');
        console.log('  node migrate.js up                    - Run all pending migrations');
        console.log('  node migrate.js rollback <filename>   - Rollback specific migration');
        process.exit(1);
}
