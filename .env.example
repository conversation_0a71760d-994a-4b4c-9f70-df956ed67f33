# Server Configuration
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=russian_flashcards
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_SSL=false

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRES_IN=7d

# Google Text-to-Speech API
GOOGLE_TTS_API_KEY=your_google_tts_api_key_here

# Forvo API (alternative to Google TTS)
FORVO_API_KEY=your_forvo_api_key_here

# Email Configuration (for password reset)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your_session_secret_here

# External APIs
YANDEX_DICTIONARY_API_KEY=your_yandex_api_key_here
