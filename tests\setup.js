// Test setup file
require('dotenv').config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Mock console methods to reduce noise during testing
global.console = {
    ...console,
    // Uncomment to suppress logs during testing
    // log: jest.fn(),
    // warn: jest.fn(),
    // error: jest.fn(),
};

// Global test timeout
jest.setTimeout(10000);

// Setup database connection for tests
const { testConnection } = require('../config/database');

beforeAll(async () => {
    // Ensure database connection is working
    const connected = await testConnection();
    if (!connected) {
        throw new Error('Database connection failed for tests');
    }
});

// Clean up after all tests
afterAll(async () => {
    // Close database connections
    const { closePool } = require('../config/database');
    await closePool();
});
