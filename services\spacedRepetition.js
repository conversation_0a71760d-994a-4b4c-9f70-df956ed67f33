/**
 * Spaced Repetition Algorithm Implementation
 * Based on the SuperMemo SM-2 algorithm with modifications
 */

// Default ease factor for new cards
const DEFAULT_EASE_FACTOR = 2.5;

// Minimum and maximum ease factors
const MIN_EASE_FACTOR = 1.3;
const MAX_EASE_FACTOR = 3.0;

// Initial intervals for new cards (in days)
const INITIAL_INTERVALS = [1, 6]; // First correct: 1 day, Second correct: 6 days

/**
 * Calculate the next review date and update spaced repetition parameters
 * @param {Object} currentData - Current practice session data
 * @param {boolean} isCorrect - Whether the answer was correct
 * @returns {Object} Updated spaced repetition data
 */
function updateSpacedRepetition(currentData, isCorrect) {
    const {
        correct_count: correctCount = 0,
        incorrect_count: incorrectCount = 0,
        ease_factor: easeFactor = DEFAULT_EASE_FACTOR,
        interval_days: intervalDays = 1,
        repetition_number: repetitionNumber = 0
    } = currentData;

    let newCorrectCount = correctCount;
    let newIncorrectCount = incorrectCount;
    let newEaseFactor = easeFactor;
    let newIntervalDays = intervalDays;
    let newRepetitionNumber = repetitionNumber;

    if (isCorrect) {
        newCorrectCount += 1;
        newRepetitionNumber += 1;

        // Calculate new interval based on repetition number
        if (newRepetitionNumber === 1) {
            newIntervalDays = INITIAL_INTERVALS[0]; // 1 day
        } else if (newRepetitionNumber === 2) {
            newIntervalDays = INITIAL_INTERVALS[1]; // 6 days
        } else {
            // For subsequent repetitions, multiply by ease factor
            newIntervalDays = Math.round(intervalDays * newEaseFactor);
        }

        // Increase ease factor slightly for correct answers (max 3.0)
        newEaseFactor = Math.min(MAX_EASE_FACTOR, newEaseFactor + 0.1);

    } else {
        newIncorrectCount += 1;
        
        // Reset repetition number for incorrect answers
        newRepetitionNumber = 0;
        
        // Decrease ease factor for incorrect answers (min 1.3)
        newEaseFactor = Math.max(MIN_EASE_FACTOR, newEaseFactor - 0.2);
        
        // Reset interval to 1 day for incorrect answers
        newIntervalDays = 1;
    }

    // Calculate next review date
    const nextReview = new Date();
    nextReview.setDate(nextReview.getDate() + newIntervalDays);

    return {
        correctCount: newCorrectCount,
        incorrectCount: newIncorrectCount,
        easeFactor: Math.round(newEaseFactor * 100) / 100, // Round to 2 decimal places
        intervalDays: newIntervalDays,
        repetitionNumber: newRepetitionNumber,
        nextReview: nextReview.toISOString()
    };
}

/**
 * Calculate the next review date without updating other parameters
 * @param {number} intervalDays - Current interval in days
 * @param {number} easeFactor - Current ease factor
 * @param {boolean} isCorrect - Whether the answer was correct
 * @returns {Date} Next review date
 */
function calculateNextReview(intervalDays, easeFactor, isCorrect) {
    let nextInterval;
    
    if (isCorrect) {
        nextInterval = Math.round(intervalDays * easeFactor);
    } else {
        nextInterval = 1; // Reset to 1 day for incorrect answers
    }
    
    const nextReview = new Date();
    nextReview.setDate(nextReview.getDate() + nextInterval);
    
    return nextReview;
}

/**
 * Get the difficulty level based on ease factor and performance
 * @param {Object} practiceData - Practice session data
 * @returns {number} Difficulty level (1-5)
 */
function getDifficultyLevel(practiceData) {
    const { ease_factor: easeFactor, correct_count: correctCount, incorrect_count: incorrectCount } = practiceData;
    
    const totalAttempts = correctCount + incorrectCount;
    const accuracy = totalAttempts > 0 ? correctCount / totalAttempts : 0;
    
    // Calculate difficulty based on ease factor and accuracy
    if (easeFactor >= 2.8 && accuracy >= 0.8) {
        return 1; // Very Easy
    } else if (easeFactor >= 2.5 && accuracy >= 0.6) {
        return 2; // Easy
    } else if (easeFactor >= 2.0 && accuracy >= 0.4) {
        return 3; // Medium
    } else if (easeFactor >= 1.6 && accuracy >= 0.2) {
        return 4; // Hard
    } else {
        return 5; // Very Hard
    }
}

/**
 * Determine if a card should be included in today's practice session
 * @param {Object} practiceData - Practice session data
 * @returns {boolean} Whether the card is due for review
 */
function isCardDueForReview(practiceData) {
    const { next_review: nextReview } = practiceData;
    const now = new Date();
    const reviewDate = new Date(nextReview);
    
    return reviewDate <= now;
}

/**
 * Calculate the priority score for a card (higher score = higher priority)
 * @param {Object} practiceData - Practice session data
 * @returns {number} Priority score
 */
function calculateCardPriority(practiceData) {
    const {
        next_review: nextReview,
        ease_factor: easeFactor,
        incorrect_count: incorrectCount,
        interval_days: intervalDays
    } = practiceData;
    
    const now = new Date();
    const reviewDate = new Date(nextReview);
    const daysOverdue = Math.max(0, (now - reviewDate) / (1000 * 60 * 60 * 24));
    
    // Higher priority for:
    // - Overdue cards
    // - Cards with lower ease factor (more difficult)
    // - Cards with more incorrect answers
    // - Cards with shorter intervals (less established)
    
    let priority = 0;
    
    // Overdue bonus (exponential)
    priority += Math.pow(daysOverdue, 1.5) * 10;
    
    // Difficulty bonus (inverse of ease factor)
    priority += (MAX_EASE_FACTOR - easeFactor) * 5;
    
    // Error count bonus
    priority += incorrectCount * 2;
    
    // Interval bonus (inverse relationship)
    priority += Math.max(0, 30 - intervalDays) * 0.5;
    
    return Math.round(priority * 100) / 100;
}

/**
 * Get recommended study session size based on user's performance
 * @param {Object} userStats - User's practice statistics
 * @returns {number} Recommended number of cards for session
 */
function getRecommendedSessionSize(userStats) {
    const {
        totalCards = 0,
        averageAccuracy = 0,
        averageSessionDuration = 0,
        studyStreak = 0
    } = userStats;
    
    let baseSize = 12; // Default session size
    
    // Adjust based on total cards
    if (totalCards < 20) {
        baseSize = Math.min(totalCards, 8);
    } else if (totalCards > 100) {
        baseSize = 15;
    }
    
    // Adjust based on accuracy
    if (averageAccuracy > 0.8) {
        baseSize += 3; // Increase for high performers
    } else if (averageAccuracy < 0.5) {
        baseSize -= 2; // Decrease for struggling users
    }
    
    // Adjust based on study streak
    if (studyStreak > 7) {
        baseSize += 2; // Reward consistent users
    }
    
    return Math.max(5, Math.min(20, baseSize)); // Keep between 5-20 cards
}

module.exports = {
    updateSpacedRepetition,
    calculateNextReview,
    getDifficultyLevel,
    isCardDueForReview,
    calculateCardPriority,
    getRecommendedSessionSize,
    DEFAULT_EASE_FACTOR,
    MIN_EASE_FACTOR,
    MAX_EASE_FACTOR,
    INITIAL_INTERVALS
};
