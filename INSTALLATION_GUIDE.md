# Installation Guide for Windows 11

This guide will help you install all necessary tools to run the Russian Flashcards platform on Windows 11.

## Required Software

### 1. Git for Windows
**Purpose**: Version control and repository management

**Installation Steps**:
1. Go to https://git-scm.com/download/win
2. Download "64-bit Git for Windows Setup"
3. Run the installer with default settings
4. **Important**: Make sure "Git from the command line and also from 3rd-party software" is selected
5. Complete the installation and restart your terminal

**Verify Installation**:
```cmd
git --version
```

### 2. Node.js and npm
**Purpose**: JavaScript runtime and package manager

**Installation Steps**:
1. Go to https://nodejs.org/
2. Download the "LTS" version (recommended for most users)
3. Run the installer with default settings
4. **Important**: Make sure "Add to PATH" is checked
5. Complete the installation and restart your terminal

**Verify Installation**:
```cmd
node --version
npm --version
```

### 3. PostgreSQL Database
**Purpose**: Database for storing flashcards and user data

**Installation Steps**:
1. Go to https://www.postgresql.org/download/windows/
2. Download the PostgreSQL installer
3. Run the installer and follow these settings:
   - **Port**: 5432 (default)
   - **Superuser password**: Choose a secure password and remember it
   - **Locale**: Default locale
4. Complete the installation

**Verify Installation**:
```cmd
psql --version
```

### 4. Visual Studio Code (Optional but Recommended)
**Purpose**: Code editor for development

**Installation Steps**:
1. Go to https://code.visualstudio.com/
2. Download "Windows" version
3. Run the installer with default settings
4. **Recommended Extensions**:
   - JavaScript (ES6) code snippets
   - PostgreSQL
   - Git History

## Alternative Installation Methods

### Using Windows Package Manager (winget)
If you prefer command-line installation, you can use winget:

```powershell
# Install Git
winget install --id Git.Git -e --source winget

# Install Node.js
winget install --id OpenJS.NodeJS -e --source winget

# Install PostgreSQL
winget install --id PostgreSQL.PostgreSQL -e --source winget

# Install Visual Studio Code
winget install --id Microsoft.VisualStudioCode -e --source winget
```

### Using Chocolatey (Requires Admin Rights)
If you have Chocolatey installed:

```cmd
# Install all tools at once
choco install git nodejs postgresql vscode -y
```

## Post-Installation Setup

### 1. Configure Git
After installing Git, configure it with your information:

```cmd
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### 2. Verify All Installations
Run these commands to verify everything is installed correctly:

```cmd
git --version
node --version
npm --version
psql --version
```

### 3. Create PostgreSQL Database
1. Open Command Prompt or PowerShell as Administrator
2. Switch to postgres user:
   ```cmd
   psql -U postgres
   ```
3. Create the database:
   ```sql
   CREATE DATABASE russian_flashcards;
   \q
   ```

## Troubleshooting

### Git Not Found
- Restart your terminal/command prompt
- Check if Git is in your PATH: `echo $env:PATH` (PowerShell) or `echo %PATH%` (CMD)
- Reinstall Git and ensure "Add to PATH" is selected

### Node.js/npm Not Found
- Restart your terminal/command prompt
- Check if Node.js is in your PATH
- Reinstall Node.js and ensure "Add to PATH" is selected

### PostgreSQL Connection Issues
- Make sure PostgreSQL service is running:
  ```cmd
  net start postgresql-x64-14
  ```
- Check if you can connect:
  ```cmd
  psql -U postgres -h localhost
  ```

### Permission Issues
- Run Command Prompt or PowerShell as Administrator
- On Windows 11, you might need to enable Developer Mode:
  - Settings → Privacy & Security → For developers → Developer Mode

## Next Steps

Once all software is installed:

1. **Clone or Initialize Repository**:
   ```cmd
   cd C:\Users\<USER>\Documents\augment-projects\FlashCards
   git init
   ```

2. **Install Project Dependencies**:
   ```cmd
   npm install
   ```

3. **Set Up Environment**:
   ```cmd
   copy .env.example .env
   ```
   Then edit `.env` with your database credentials

4. **Set Up Database**:
   ```cmd
   psql -U postgres -d russian_flashcards -f database/schema.sql
   ```

5. **Start the Application**:
   ```cmd
   npm run dev
   ```

## Getting Help

If you encounter issues:

1. **Check the error messages** carefully
2. **Restart your terminal** after installations
3. **Run as Administrator** if you get permission errors
4. **Check Windows Defender** - it might block downloads
5. **Verify PATH variables** are set correctly

## System Requirements

- **OS**: Windows 11 (or Windows 10 version 1903+)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space for all tools
- **Internet**: Required for downloading packages and APIs

## Security Notes

- **Windows Defender**: May flag some installers as potentially unwanted
- **Execution Policy**: PowerShell scripts might be blocked by default
- **Firewall**: PostgreSQL and Node.js might need firewall exceptions
- **Antivirus**: Some antivirus software might interfere with installations

To allow PowerShell scripts temporarily:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## Contact Information

If you need additional help with installation:
- Check the main README.md for project-specific setup
- Review the troubleshooting section above
- Ensure all prerequisites are met before proceeding
