#!/bin/bash

echo "============================================"
echo "Russian Flashcards - Docker Quick Start"
echo "============================================"
echo

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "[ERROR] Docker is not running or not installed."
    echo "Please start Docker and try again."
    echo
    echo "To install Docker:"
    echo "- macOS: Download Docker Desktop from https://www.docker.com/products/docker-desktop/"
    echo "- Linux: Follow instructions at https://docs.docker.com/engine/install/"
    echo
    exit 1
fi

echo "[✓] Docker is running"

# Check if .env file exists
if [ ! -f .env ]; then
    echo
    echo "Creating .env file from template..."
    cp .env.docker .env
    echo "[✓] Created .env file"
    echo
    echo "IMPORTANT: Please edit .env file and add your API keys:"
    echo "- GOOGLE_TTS_API_KEY (for audio generation)"
    echo "- FORVO_API_KEY (optional, for audio fallback)"
    echo
    echo "Press any key to continue with default settings..."
    read -n 1 -s
else
    echo "[✓] .env file exists"
fi

echo
echo "Starting Russian Flashcards in Docker..."
echo

# Start development environment
echo "Starting development environment..."
docker-compose -f docker-compose.dev.yml up --build -d

if [ $? -eq 0 ]; then
    echo
    echo "============================================"
    echo "🎉 Russian Flashcards is starting up!"
    echo "============================================"
    echo
    echo "Services:"
    echo "- Application: http://localhost:3001"
    echo "- Database: localhost:5433 (PostgreSQL)"
    echo "- pgAdmin: http://localhost:8080"
    echo "  - Email: <EMAIL>"
    echo "  - Password: admin123"
    echo "- Redis: localhost:6380"
    echo
    echo "Logs:"
    echo "- View all logs: docker-compose -f docker-compose.dev.yml logs -f"
    echo "- View app logs: docker-compose -f docker-compose.dev.yml logs -f app"
    echo
    echo "Management:"
    echo "- Stop: docker-compose -f docker-compose.dev.yml down"
    echo "- Restart: docker-compose -f docker-compose.dev.yml restart"
    echo "- Rebuild: docker-compose -f docker-compose.dev.yml up --build"
    echo
    echo "The application will be ready in about 30-60 seconds..."
    echo "Opening browser in 10 seconds..."
    
    sleep 10
    
    # Try to open browser (works on macOS and some Linux distributions)
    if command -v open >/dev/null 2>&1; then
        open http://localhost:3001
    elif command -v xdg-open >/dev/null 2>&1; then
        xdg-open http://localhost:3001
    else
        echo "Please open http://localhost:3001 in your browser"
    fi
    
else
    echo
    echo "[ERROR] Failed to start containers."
    echo "Please check the error messages above."
    echo
    echo "Common solutions:"
    echo "1. Make sure Docker is running"
    echo "2. Check if ports 3001, 5433, 6380, 8080 are available"
    echo "3. Try: docker-compose -f docker-compose.dev.yml down"
    echo "4. Then run this script again"
fi

echo
echo "Press any key to exit..."
read -n 1 -s
