const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticateAdmin, auditAdminAction } = require('../middleware/adminAuth');

const router = express.Router();

// Apply admin authentication to all routes
router.use(authenticateAdmin);

// Admin Dashboard - Get overview metrics
router.get('/dashboard', auditAdminAction('VIEW_DASHBOARD'), async (req, res) => {
    try {
        // Get pending requests count
        const pendingRequestsResult = await query(
            'SELECT COUNT(*) as count FROM word_requests WHERE status = $1',
            ['pending']
        );

        // Get total users count
        const totalUsersResult = await query(
            'SELECT COUNT(*) as count FROM users WHERE is_active = true'
        );

        // Get total cards count
        const totalCardsResult = await query(
            'SELECT COUNT(*) as count FROM flashcards'
        );

        // Get total sets count
        const totalSetsResult = await query(
            'SELECT COUNT(*) as count FROM practice_sets WHERE is_active = true'
        );

        // Get recent activity (last 10 word requests)
        const recentActivityResult = await query(`
            SELECT 
                wr.request_id,
                wr.russian_word,
                wr.status,
                wr.submitted_at,
                u.email as user_email
            FROM word_requests wr
            JOIN users u ON wr.user_id = u.user_id
            ORDER BY wr.submitted_at DESC
            LIMIT 10
        `);

        res.json({
            pendingRequests: parseInt(pendingRequestsResult.rows[0].count),
            totalUsers: parseInt(totalUsersResult.rows[0].count),
            totalCards: parseInt(totalCardsResult.rows[0].count),
            totalSets: parseInt(totalSetsResult.rows[0].count),
            recentActivity: recentActivityResult.rows.map(row => ({
                type: 'word_request',
                requestId: row.request_id,
                word: row.russian_word,
                status: row.status,
                userEmail: row.user_email,
                timestamp: row.submitted_at
            }))
        });

    } catch (error) {
        console.error('Admin dashboard error:', error);
        res.status(500).json({
            message: 'Internal server error while loading dashboard'
        });
    }
});

// Get all word requests with pagination
router.get('/requests', auditAdminAction('VIEW_REQUESTS'), async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const status = req.query.status || 'all';
        const offset = (page - 1) * limit;

        let whereClause = '';
        let params = [limit, offset];
        
        if (status !== 'all') {
            whereClause = 'WHERE wr.status = $3';
            params.push(status);
        }

        const requestsResult = await query(`
            SELECT 
                wr.request_id,
                wr.russian_word,
                wr.comments,
                wr.status,
                wr.admin_notes,
                wr.submitted_at,
                wr.processed_at,
                u.email as user_email,
                admin.email as processed_by_email
            FROM word_requests wr
            JOIN users u ON wr.user_id = u.user_id
            LEFT JOIN users admin ON wr.processed_by = admin.user_id
            ${whereClause}
            ORDER BY wr.submitted_at DESC
            LIMIT $1 OFFSET $2
        `, params);

        // Get total count for pagination
        let countParams = [];
        if (status !== 'all') {
            countParams.push(status);
            whereClause = 'WHERE status = $1';
        }

        const countResult = await query(
            `SELECT COUNT(*) as total FROM word_requests ${whereClause}`,
            countParams
        );

        res.json({
            requests: requestsResult.rows,
            pagination: {
                page,
                limit,
                total: parseInt(countResult.rows[0].total),
                totalPages: Math.ceil(countResult.rows[0].total / limit)
            }
        });

    } catch (error) {
        console.error('Admin requests error:', error);
        res.status(500).json({
            message: 'Internal server error while loading requests'
        });
    }
});

// Approve word request
router.post('/requests/:id/approve', 
    auditAdminAction('APPROVE_REQUEST'),
    [body('adminNotes').optional().trim().isLength({ max: 500 })],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const requestId = req.params.id;
            const { adminNotes } = req.body;

            // Update request status
            const result = await query(`
                UPDATE word_requests 
                SET status = 'approved', 
                    admin_notes = $1, 
                    processed_by = $2, 
                    processed_at = CURRENT_TIMESTAMP
                WHERE request_id = $3 AND status = 'pending'
                RETURNING russian_word, user_id
            `, [adminNotes || null, req.user.userId, requestId]);

            if (result.rows.length === 0) {
                return res.status(404).json({
                    message: 'Request not found or already processed'
                });
            }

            res.json({
                message: 'Request approved successfully',
                requestId,
                word: result.rows[0].russian_word
            });

        } catch (error) {
            console.error('Approve request error:', error);
            res.status(500).json({
                message: 'Internal server error while approving request'
            });
        }
    }
);

// Reject word request
router.post('/requests/:id/reject',
    auditAdminAction('REJECT_REQUEST'),
    [body('adminNotes').optional().trim().isLength({ max: 500 })],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const requestId = req.params.id;
            const { adminNotes } = req.body;

            // Update request status
            const result = await query(`
                UPDATE word_requests 
                SET status = 'rejected', 
                    admin_notes = $1, 
                    processed_by = $2, 
                    processed_at = CURRENT_TIMESTAMP
                WHERE request_id = $3 AND status = 'pending'
                RETURNING russian_word
            `, [adminNotes || null, req.user.userId, requestId]);

            if (result.rows.length === 0) {
                return res.status(404).json({
                    message: 'Request not found or already processed'
                });
            }

            res.json({
                message: 'Request rejected successfully',
                requestId,
                word: result.rows[0].russian_word
            });

        } catch (error) {
            console.error('Reject request error:', error);
            res.status(500).json({
                message: 'Internal server error while rejecting request'
            });
        }
    }
);

// Admin Card Management

// Get all cards with pagination and filtering
router.get('/cards', auditAdminAction('VIEW_CARDS'), async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const search = req.query.search || '';
        const tags = req.query.tags ? req.query.tags.split(',') : [];
        const offset = (page - 1) * limit;

        let whereConditions = [];
        let params = [limit, offset];
        let paramIndex = 3;

        if (search) {
            whereConditions.push(`(russian_word ILIKE $${paramIndex} OR english_translations::text ILIKE $${paramIndex})`);
            params.push(`%${search}%`);
            paramIndex++;
        }

        if (tags.length > 0) {
            whereConditions.push(`tags ?| $${paramIndex}`);
            params.push(tags);
            paramIndex++;
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const cardsResult = await query(`
            SELECT
                card_id,
                russian_word,
                english_translations,
                ipa_transcription,
                audio_url,
                example_sentence,
                image_url,
                difficulty_level,
                tags,
                created_by,
                created_at,
                updated_at
            FROM flashcards
            ${whereClause}
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
        `, params);

        // Get total count
        const countParams = params.slice(2); // Remove limit and offset
        const countResult = await query(
            `SELECT COUNT(*) as total FROM flashcards ${whereClause}`,
            countParams
        );

        res.json({
            cards: cardsResult.rows,
            pagination: {
                page,
                limit,
                total: parseInt(countResult.rows[0].total),
                totalPages: Math.ceil(countResult.rows[0].total / limit)
            }
        });

    } catch (error) {
        console.error('Admin get cards error:', error);
        res.status(500).json({
            message: 'Internal server error while loading cards'
        });
    }
});

// Create new card from approved request
router.post('/cards/from-request/:requestId',
    auditAdminAction('CREATE_CARD_FROM_REQUEST'),
    [
        body('englishTranslations').isArray({ min: 1 }).withMessage('At least one English translation is required'),
        body('englishTranslations.*').trim().notEmpty().withMessage('Translation cannot be empty'),
        body('ipaTranscription').optional().trim(),
        body('exampleSentence').optional().trim().isLength({ max: 250 }),
        body('imageUrl').optional().isURL(),
        body('difficultyLevel').optional().isInt({ min: 1, max: 5 }),
        body('tags').optional().isArray()
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const requestId = req.params.requestId;
            const {
                englishTranslations,
                ipaTranscription,
                exampleSentence,
                imageUrl,
                difficultyLevel = 1,
                tags = []
            } = req.body;

            // Get the approved request
            const requestResult = await query(
                'SELECT russian_word, user_id FROM word_requests WHERE request_id = $1 AND status = $2',
                [requestId, 'approved']
            );

            if (requestResult.rows.length === 0) {
                return res.status(404).json({
                    message: 'Approved request not found'
                });
            }

            const { russian_word: russianWord } = requestResult.rows[0];

            // Check if card already exists
            const existingCard = await query(
                'SELECT card_id FROM flashcards WHERE russian_word = $1',
                [russianWord]
            );

            if (existingCard.rows.length > 0) {
                return res.status(409).json({
                    message: 'Card for this word already exists'
                });
            }

            // Generate audio
            const { generateAudio } = require('../services/audioService');
            let audioUrl = null;
            try {
                audioUrl = await generateAudio(russianWord);
            } catch (audioError) {
                console.warn('Audio generation failed:', audioError.message);
            }

            // Create flashcard
            const cardResult = await query(`
                INSERT INTO flashcards
                (russian_word, english_translations, ipa_transcription, audio_url,
                 example_sentence, image_url, difficulty_level, tags, created_by)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING card_id, russian_word, english_translations, created_at
            `, [
                russianWord,
                JSON.stringify(englishTranslations),
                ipaTranscription,
                audioUrl,
                exampleSentence,
                imageUrl,
                difficultyLevel,
                JSON.stringify(tags),
                req.user.userId
            ]);

            res.status(201).json({
                message: 'Card created successfully from request',
                card: cardResult.rows[0],
                requestId
            });

        } catch (error) {
            console.error('Create card from request error:', error);
            res.status(500).json({
                message: 'Internal server error while creating card'
            });
        }
    }
);

// Get all cards for admin management
router.get('/cards', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';

        let whereClause = '';
        let params = [];
        let paramIndex = 1;

        if (search) {
            whereClause = `WHERE russian_word ILIKE $${paramIndex} OR english_translations::text ILIKE $${paramIndex}`;
            params.push(`%${search}%`);
            paramIndex++;
        }

        // Get total count
        const countResult = await query(`
            SELECT COUNT(*) as total
            FROM flashcards
            ${whereClause}
        `, params);

        // Get cards
        const cardsResult = await query(`
            SELECT
                card_id,
                russian_word,
                english_translations,
                ipa_transcription,
                audio_url,
                part_of_speech,
                number_type,
                gender,
                flashcard_sets,
                author_name,
                author_link,
                image_url,
                google_tts_audio_url,
                use_google_tts,
                created_at,
                updated_at,
                created_by
            FROM flashcards
            ${whereClause}
            ORDER BY created_at DESC
            LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `, [...params, limit, offset]);

        res.json({
            cards: cardsResult.rows,
            pagination: {
                page,
                limit,
                total: parseInt(countResult.rows[0].total),
                totalPages: Math.ceil(countResult.rows[0].total / limit)
            }
        });

    } catch (error) {
        console.error('Get cards error:', error);
        res.status(500).json({
            message: 'Internal server error while fetching cards'
        });
    }
});

// Create new card directly (not from request)
router.post('/cards',
    auditAdminAction('CREATE_CARD'),
    [
        body('russianWord').trim().notEmpty().withMessage('Russian word is required'),
        body('englishTranslations').isArray({ min: 1 }).withMessage('At least one English translation is required'),
        body('englishTranslations.*').trim().notEmpty().withMessage('Translation cannot be empty'),
        body('ipaTranscription').optional().trim(),
        body('partOfSpeech').optional().trim(),
        body('numberType').optional().trim(),
        body('gender').optional().trim(),
        body('flashcardSets').optional().isArray(),
        body('authorName').optional().trim(),
        body('authorLink').optional({ checkFalsy: true }).isURL(),
        body('imageUrl').optional({ checkFalsy: true }).isURL(),
        body('useGoogleTts').optional().isBoolean()
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const {
                russianWord,
                englishTranslations,
                ipaTranscription,
                partOfSpeech,
                numberType,
                gender,
                flashcardSets = [],
                authorName,
                authorLink,
                imageUrl,
                useGoogleTts = true
            } = req.body;

            // Check if card already exists
            const existingCard = await query(
                'SELECT card_id FROM flashcards WHERE russian_word = $1',
                [russianWord]
            );

            if (existingCard.rows.length > 0) {
                return res.status(409).json({
                    message: 'Card for this word already exists'
                });
            }

            // Generate audio if requested
            let audioUrl = null;
            let googleTtsAudioUrl = null;

            if (useGoogleTts) {
                try {
                    const { generateAudio } = require('../services/audioService');
                    googleTtsAudioUrl = await generateAudio(russianWord);
                    audioUrl = googleTtsAudioUrl; // Use Google TTS as primary audio
                } catch (audioError) {
                    console.warn('Google TTS audio generation failed:', audioError.message);
                }
            }

            // Create flashcard with new fields
            const cardResult = await query(`
                INSERT INTO flashcards
                (russian_word, english_translations, ipa_transcription, audio_url,
                 part_of_speech, number_type, gender, flashcard_sets, author_name,
                 author_link, image_url, google_tts_audio_url, use_google_tts, created_by)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                RETURNING card_id, russian_word, english_translations, created_at
            `, [
                russianWord,
                JSON.stringify(englishTranslations),
                ipaTranscription,
                audioUrl,
                partOfSpeech,
                numberType,
                gender,
                JSON.stringify(flashcardSets),
                authorName,
                authorLink,
                imageUrl,
                googleTtsAudioUrl,
                useGoogleTts,
                req.user.userId
            ]);

            res.status(201).json({
                message: 'Card created successfully',
                card: cardResult.rows[0]
            });

        } catch (error) {
            console.error('Create card error:', error);
            res.status(500).json({
                message: 'Internal server error while creating card'
            });
        }
    }
);

// Update existing card
router.put('/cards/:id',
    auditAdminAction('UPDATE_CARD'),
    [
        body('russianWord').optional().trim().notEmpty(),
        body('englishTranslations').optional().isArray({ min: 1 }),
        body('englishTranslations.*').optional().trim().notEmpty(),
        body('ipaTranscription').optional().trim(),
        body('partOfSpeech').optional().trim(),
        body('numberType').optional().trim(),
        body('gender').optional().trim(),
        body('flashcardSets').optional().isArray(),
        body('authorName').optional().trim(),
        body('authorLink').optional({ checkFalsy: true }).isURL(),
        body('imageUrl').optional({ checkFalsy: true }).isURL(),
        body('useGoogleTts').optional().isBoolean()
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const cardId = req.params.id;
            const {
                russianWord,
                englishTranslations,
                ipaTranscription,
                partOfSpeech,
                numberType,
                gender,
                flashcardSets,
                authorName,
                authorLink,
                imageUrl,
                useGoogleTts
            } = req.body;

            // Build dynamic update query
            const updates = [];
            const params = [];
            let paramIndex = 1;

            if (russianWord !== undefined) {
                updates.push(`russian_word = $${paramIndex}`);
                params.push(russianWord);
                paramIndex++;
            }

            if (englishTranslations !== undefined) {
                updates.push(`english_translations = $${paramIndex}`);
                params.push(JSON.stringify(englishTranslations));
                paramIndex++;
            }

            if (ipaTranscription !== undefined) {
                updates.push(`ipa_transcription = $${paramIndex}`);
                params.push(ipaTranscription);
                paramIndex++;
            }

            if (partOfSpeech !== undefined) {
                updates.push(`part_of_speech = $${paramIndex}`);
                params.push(partOfSpeech);
                paramIndex++;
            }

            if (numberType !== undefined) {
                updates.push(`number_type = $${paramIndex}`);
                params.push(numberType);
                paramIndex++;
            }

            if (gender !== undefined) {
                updates.push(`gender = $${paramIndex}`);
                params.push(gender);
                paramIndex++;
            }

            if (flashcardSets !== undefined) {
                updates.push(`flashcard_sets = $${paramIndex}`);
                params.push(JSON.stringify(flashcardSets));
                paramIndex++;
            }

            if (authorName !== undefined) {
                updates.push(`author_name = $${paramIndex}`);
                params.push(authorName);
                paramIndex++;
            }

            if (authorLink !== undefined) {
                updates.push(`author_link = $${paramIndex}`);
                params.push(authorLink);
                paramIndex++;
            }

            if (imageUrl !== undefined) {
                updates.push(`image_url = $${paramIndex}`);
                params.push(imageUrl);
                paramIndex++;
            }

            if (useGoogleTts !== undefined) {
                updates.push(`use_google_tts = $${paramIndex}`);
                params.push(useGoogleTts);
                paramIndex++;

                // Regenerate audio if Google TTS setting changed
                if (useGoogleTts && russianWord) {
                    try {
                        const { generateAudio } = require('../services/audioService');
                        const googleTtsAudioUrl = await generateAudio(russianWord);
                        updates.push(`google_tts_audio_url = $${paramIndex}`);
                        params.push(googleTtsAudioUrl);
                        paramIndex++;
                        updates.push(`audio_url = $${paramIndex}`);
                        params.push(googleTtsAudioUrl);
                        paramIndex++;
                    } catch (audioError) {
                        console.warn('Google TTS audio generation failed:', audioError.message);
                    }
                }
            }

            if (updates.length === 0) {
                return res.status(400).json({
                    message: 'No valid updates provided'
                });
            }

            updates.push(`updated_at = CURRENT_TIMESTAMP`);
            params.push(cardId);

            const result = await query(`
                UPDATE flashcards
                SET ${updates.join(', ')}
                WHERE card_id = $${paramIndex}
                RETURNING card_id, russian_word, english_translations, updated_at
            `, params);

            if (result.rows.length === 0) {
                return res.status(404).json({
                    message: 'Card not found'
                });
            }

            res.json({
                message: 'Card updated successfully',
                card: result.rows[0]
            });

        } catch (error) {
            console.error('Update card error:', error);
            res.status(500).json({
                message: 'Internal server error while updating card'
            });
        }
    }
);

// Delete card
router.delete('/cards/:id', auditAdminAction('DELETE_CARD'), async (req, res) => {
    try {
        const cardId = req.params.id;

        const result = await query(`
            DELETE FROM flashcards
            WHERE card_id = $1
            RETURNING russian_word
        `, [cardId]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                message: 'Card not found'
            });
        }

        res.json({
            message: 'Card deleted successfully',
            word: result.rows[0].russian_word
        });

    } catch (error) {
        console.error('Delete card error:', error);
        res.status(500).json({
            message: 'Internal server error while deleting card'
        });
    }
});

// Admin User Management

// Get all users with pagination
router.get('/users', auditAdminAction('VIEW_USERS'), async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const search = req.query.search || '';
        const status = req.query.status || 'all'; // all, active, inactive, admin
        const offset = (page - 1) * limit;

        let whereConditions = [];
        let params = [limit, offset];
        let paramIndex = 3;

        if (search) {
            whereConditions.push(`(email ILIKE $${paramIndex} OR first_name ILIKE $${paramIndex} OR last_name ILIKE $${paramIndex})`);
            params.push(`%${search}%`);
            paramIndex++;
        }

        if (status === 'active') {
            whereConditions.push('is_active = true AND is_admin = false');
        } else if (status === 'inactive') {
            whereConditions.push('is_active = false');
        } else if (status === 'admin') {
            whereConditions.push('is_admin = true');
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const usersResult = await query(`
            SELECT
                user_id,
                email,
                first_name,
                last_name,
                is_admin,
                is_active,
                email_verified,
                created_at,
                last_login,
                (SELECT COUNT(*) FROM word_requests WHERE user_id = users.user_id) as request_count,
                (SELECT COUNT(*) FROM practice_sessions WHERE user_id = users.user_id) as practice_count
            FROM users
            ${whereClause}
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
        `, params);

        // Get total count
        const countParams = params.slice(2);
        const countResult = await query(
            `SELECT COUNT(*) as total FROM users ${whereClause}`,
            countParams
        );

        res.json({
            users: usersResult.rows,
            pagination: {
                page,
                limit,
                total: parseInt(countResult.rows[0].total),
                totalPages: Math.ceil(countResult.rows[0].total / limit)
            }
        });

    } catch (error) {
        console.error('Admin get users error:', error);
        res.status(500).json({
            message: 'Internal server error while loading users'
        });
    }
});

// Get specific user details
router.get('/users/:id', auditAdminAction('VIEW_USER_DETAILS'), async (req, res) => {
    try {
        const userId = req.params.id;

        const userResult = await query(`
            SELECT
                user_id,
                email,
                first_name,
                last_name,
                is_admin,
                is_active,
                email_verified,
                created_at,
                updated_at,
                last_login
            FROM users
            WHERE user_id = $1
        `, [userId]);

        if (userResult.rows.length === 0) {
            return res.status(404).json({
                message: 'User not found'
            });
        }

        // Get user statistics
        const statsResult = await query(`
            SELECT
                (SELECT COUNT(*) FROM word_requests WHERE user_id = $1) as total_requests,
                (SELECT COUNT(*) FROM word_requests WHERE user_id = $1 AND status = 'pending') as pending_requests,
                (SELECT COUNT(*) FROM word_requests WHERE user_id = $1 AND status = 'approved') as approved_requests,
                (SELECT COUNT(*) FROM practice_sessions WHERE user_id = $1) as practice_sessions,
                (SELECT COUNT(*) FROM user_card_overlays WHERE user_id = $1) as overlays_created
        `, [userId]);

        res.json({
            user: userResult.rows[0],
            stats: statsResult.rows[0]
        });

    } catch (error) {
        console.error('Admin get user details error:', error);
        res.status(500).json({
            message: 'Internal server error while loading user details'
        });
    }
});

// Update user status (activate/deactivate/promote/demote)
router.put('/users/:id',
    auditAdminAction('UPDATE_USER'),
    [
        body('isActive').optional().isBoolean(),
        body('isAdmin').optional().isBoolean(),
        body('emailVerified').optional().isBoolean()
    ],
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.params.id;
            const { isActive, isAdmin, emailVerified } = req.body;

            // Prevent admin from deactivating themselves
            if (userId == req.user.userId && isActive === false) {
                return res.status(400).json({
                    message: 'You cannot deactivate your own account'
                });
            }

            // Prevent admin from demoting themselves if they're the only admin
            if (userId == req.user.userId && isAdmin === false) {
                const adminCountResult = await query(
                    'SELECT COUNT(*) as count FROM users WHERE is_admin = true AND is_active = true'
                );

                if (parseInt(adminCountResult.rows[0].count) <= 1) {
                    return res.status(400).json({
                        message: 'Cannot demote the last active admin'
                    });
                }
            }

            const updates = [];
            const params = [];
            let paramIndex = 1;

            if (typeof isActive === 'boolean') {
                updates.push(`is_active = $${paramIndex}`);
                params.push(isActive);
                paramIndex++;
            }

            if (typeof isAdmin === 'boolean') {
                updates.push(`is_admin = $${paramIndex}`);
                params.push(isAdmin);
                paramIndex++;
            }

            if (typeof emailVerified === 'boolean') {
                updates.push(`email_verified = $${paramIndex}`);
                params.push(emailVerified);
                paramIndex++;
            }

            if (updates.length === 0) {
                return res.status(400).json({
                    message: 'No valid updates provided'
                });
            }

            updates.push(`updated_at = CURRENT_TIMESTAMP`);
            params.push(userId);

            const result = await query(`
                UPDATE users
                SET ${updates.join(', ')}
                WHERE user_id = $${paramIndex}
                RETURNING user_id, email, first_name, last_name, is_admin, is_active, email_verified
            `, params);

            if (result.rows.length === 0) {
                return res.status(404).json({
                    message: 'User not found'
                });
            }

            res.json({
                message: 'User updated successfully',
                user: result.rows[0]
            });

        } catch (error) {
            console.error('Admin update user error:', error);
            res.status(500).json({
                message: 'Internal server error while updating user'
            });
        }
    }
);

// Delete user (soft delete by deactivating)
router.delete('/users/:id', auditAdminAction('DELETE_USER'), async (req, res) => {
    try {
        const userId = req.params.id;

        // Prevent admin from deleting themselves
        if (userId == req.user.userId) {
            return res.status(400).json({
                message: 'You cannot delete your own account'
            });
        }

        const result = await query(`
            UPDATE users
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1 AND is_active = true
            RETURNING email
        `, [userId]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                message: 'User not found or already deactivated'
            });
        }

        res.json({
            message: 'User deactivated successfully',
            email: result.rows[0].email
        });

    } catch (error) {
        console.error('Admin delete user error:', error);
        res.status(500).json({
            message: 'Internal server error while deleting user'
        });
    }
});

module.exports = router;
