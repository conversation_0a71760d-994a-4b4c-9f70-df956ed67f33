const bcrypt = require('bcryptjs');
const { Pool } = require('pg');
require('dotenv').config();

// Database connection
const pool = new Pool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'russian_flashcards',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
});

// Default admin credentials (can be overridden by environment variables)
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123456';
const ADMIN_FIRST_NAME = process.env.ADMIN_FIRST_NAME || 'Admin';
const ADMIN_LAST_NAME = process.env.ADMIN_LAST_NAME || 'User';

async function seedAdminUser() {
    try {
        console.log('🌱 Starting admin user seeding...');

        // Check if admin user already exists
        const existingAdmin = await pool.query(
            'SELECT user_id, email FROM users WHERE email = $1',
            [ADMIN_EMAIL]
        );

        if (existingAdmin.rows.length > 0) {
            console.log(`⚠️  Admin user already exists: ${ADMIN_EMAIL}`);
            
            // Update existing user to be admin if not already
            const updateResult = await pool.query(
                'UPDATE users SET is_admin = true WHERE email = $1 AND is_admin = false RETURNING user_id',
                [ADMIN_EMAIL]
            );

            if (updateResult.rows.length > 0) {
                console.log(`✅ Updated existing user to admin: ${ADMIN_EMAIL}`);
            } else {
                console.log(`ℹ️  User ${ADMIN_EMAIL} is already an admin`);
            }
            
            return;
        }

        // Hash the admin password
        console.log('🔐 Hashing admin password...');
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(ADMIN_PASSWORD, saltRounds);

        // Create admin user
        console.log('👤 Creating admin user...');
        const result = await pool.query(`
            INSERT INTO users (
                email, 
                password_hash, 
                first_name, 
                last_name, 
                is_admin,
                email_verified,
                is_active
            ) VALUES ($1, $2, $3, $4, true, true, true)
            RETURNING user_id, email, first_name, last_name
        `, [ADMIN_EMAIL, passwordHash, ADMIN_FIRST_NAME, ADMIN_LAST_NAME]);

        const adminUser = result.rows[0];

        console.log('✅ Admin user created successfully!');
        console.log('📧 Email:', adminUser.email);
        console.log('👤 Name:', `${adminUser.first_name} ${adminUser.last_name}`);
        console.log('🆔 User ID:', adminUser.user_id);
        console.log('');
        console.log('🔑 Login credentials:');
        console.log('   Email:', ADMIN_EMAIL);
        console.log('   Password:', ADMIN_PASSWORD);
        console.log('');
        console.log('⚠️  IMPORTANT: Change the default password after first login!');

    } catch (error) {
        console.error('❌ Error seeding admin user:', error);
        throw error;
    }
}

async function promoteUserToAdmin(email) {
    try {
        console.log(`🔄 Promoting user to admin: ${email}`);

        const result = await pool.query(
            'UPDATE users SET is_admin = true WHERE email = $1 AND is_active = true RETURNING user_id, email, first_name, last_name',
            [email]
        );

        if (result.rows.length === 0) {
            console.log(`❌ User not found or inactive: ${email}`);
            return false;
        }

        const user = result.rows[0];
        console.log('✅ User promoted to admin successfully!');
        console.log('📧 Email:', user.email);
        console.log('👤 Name:', `${user.first_name} ${user.last_name}`);
        console.log('🆔 User ID:', user.user_id);

        return true;
    } catch (error) {
        console.error('❌ Error promoting user to admin:', error);
        throw error;
    }
}

async function listAdminUsers() {
    try {
        console.log('👑 Current admin users:');
        
        const result = await pool.query(`
            SELECT user_id, email, first_name, last_name, created_at, last_login
            FROM users 
            WHERE is_admin = true AND is_active = true
            ORDER BY created_at ASC
        `);

        if (result.rows.length === 0) {
            console.log('   No admin users found.');
            return;
        }

        result.rows.forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.email} (${user.first_name} ${user.last_name})`);
            console.log(`      ID: ${user.user_id}`);
            console.log(`      Created: ${user.created_at}`);
            console.log(`      Last Login: ${user.last_login || 'Never'}`);
            console.log('');
        });

    } catch (error) {
        console.error('❌ Error listing admin users:', error);
        throw error;
    }
}

// Command line interface
async function main() {
    const command = process.argv[2];
    const email = process.argv[3];

    try {
        switch (command) {
            case 'seed':
                await seedAdminUser();
                break;
            
            case 'promote':
                if (!email) {
                    console.error('❌ Please provide an email address to promote');
                    console.log('Usage: node seed-admin.<NAME_EMAIL>');
                    process.exit(1);
                }
                await promoteUserToAdmin(email);
                break;
            
            case 'list':
                await listAdminUsers();
                break;
            
            default:
                console.log('🌱 Admin User Management');
                console.log('');
                console.log('Usage:');
                console.log('  node seed-admin.js seed                    - Create default admin user');
                console.log('  node seed-admin.js promote <email>         - Promote existing user to admin');
                console.log('  node seed-admin.js list                    - List all admin users');
                console.log('');
                console.log('Environment variables:');
                console.log('  ADMIN_EMAIL      - Admin email (default: <EMAIL>)');
                console.log('  ADMIN_PASSWORD   - Admin password (default: admin123456)');
                console.log('  ADMIN_FIRST_NAME - Admin first name (default: Admin)');
                console.log('  ADMIN_LAST_NAME  - Admin last name (default: User)');
                process.exit(1);
        }
    } catch (error) {
        console.error('❌ Operation failed:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    seedAdminUser,
    promoteUserToAdmin,
    listAdminUsers
};
