const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
    try {
        const result = await query(
            `SELECT 
                user_id,
                email,
                first_name,
                last_name,
                created_at,
                last_login,
                email_verified
            FROM users 
            WHERE user_id = $1 AND is_active = true`,
            [req.userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({
                message: 'User not found'
            });
        }

        const user = result.rows[0];

        // Get user statistics
        const statsResult = await query(
            `SELECT 
                COUNT(f.card_id) as total_cards,
                COUNT(CASE WHEN ps.next_review <= CURRENT_TIMESTAMP THEN 1 END) as due_cards,
                AVG(CASE WHEN ps.correct_count + ps.incorrect_count > 0 
                    THEN ps.correct_count::float / (ps.correct_count + ps.incorrect_count) 
                    ELSE 0 END) as avg_accuracy
            FROM flashcards f
            LEFT JOIN practice_sessions ps ON f.card_id = ps.card_id
            WHERE f.user_id = $1`,
            [req.userId]
        );

        const stats = statsResult.rows[0];

        // Get recent activity
        const activityResult = await query(
            `SELECT 
                session_date,
                cards_reviewed,
                cards_correct,
                cards_incorrect
            FROM session_history 
            WHERE user_id = $1 
            ORDER BY session_date DESC 
            LIMIT 7`,
            [req.userId]
        );

        res.json({
            user: {
                id: user.user_id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                createdAt: user.created_at,
                lastLogin: user.last_login,
                emailVerified: user.email_verified
            },
            statistics: {
                totalCards: parseInt(stats.total_cards) || 0,
                dueCards: parseInt(stats.due_cards) || 0,
                averageAccuracy: Math.round((parseFloat(stats.avg_accuracy) || 0) * 100)
            },
            recentActivity: activityResult.rows
        });

    } catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({
            message: 'Internal server error while fetching profile'
        });
    }
});

// Update user profile
router.put('/profile', authenticateToken, [
    body('firstName').optional().trim().isLength({ min: 1, max: 100 }).withMessage('First name must be 1-100 characters'),
    body('lastName').optional().trim().isLength({ min: 1, max: 100 }).withMessage('Last name must be 1-100 characters'),
    body('email').optional().isEmail().normalizeEmail().withMessage('Valid email is required')
], async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { firstName, lastName, email } = req.body;
        const updates = [];
        const values = [];
        let paramCount = 1;

        // Build dynamic update query
        if (firstName !== undefined) {
            updates.push(`first_name = $${paramCount++}`);
            values.push(firstName);
        }

        if (lastName !== undefined) {
            updates.push(`last_name = $${paramCount++}`);
            values.push(lastName);
        }

        if (email !== undefined) {
            // Check if email is already taken by another user
            const emailCheck = await query(
                'SELECT user_id FROM users WHERE email = $1 AND user_id != $2',
                [email, req.userId]
            );

            if (emailCheck.rows.length > 0) {
                return res.status(409).json({
                    message: 'Email is already taken by another user'
                });
            }

            updates.push(`email = $${paramCount++}`);
            values.push(email);
            
            // Reset email verification if email changed
            updates.push(`email_verified = false`);
        }

        if (updates.length === 0) {
            return res.status(400).json({
                message: 'No valid fields to update'
            });
        }

        updates.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(req.userId);

        const updateQuery = `
            UPDATE users 
            SET ${updates.join(', ')}
            WHERE user_id = $${paramCount}
            RETURNING user_id, email, first_name, last_name, updated_at, email_verified
        `;

        const result = await query(updateQuery, values);

        if (result.rows.length === 0) {
            return res.status(404).json({
                message: 'User not found'
            });
        }

        const updatedUser = result.rows[0];

        res.json({
            message: 'Profile updated successfully',
            user: {
                id: updatedUser.user_id,
                email: updatedUser.email,
                firstName: updatedUser.first_name,
                lastName: updatedUser.last_name,
                updatedAt: updatedUser.updated_at,
                emailVerified: updatedUser.email_verified
            }
        });

    } catch (error) {
        console.error('Update profile error:', error);
        res.status(500).json({
            message: 'Internal server error while updating profile'
        });
    }
});

// Change password
router.put('/password', authenticateToken, [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword').isLength({ min: 8 }).withMessage('New password must be at least 8 characters long'),
    body('confirmPassword').custom((value, { req }) => {
        if (value !== req.body.newPassword) {
            throw new Error('Password confirmation does not match');
        }
        return true;
    })
], async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { currentPassword, newPassword } = req.body;

        // Get current password hash
        const userResult = await query(
            'SELECT password_hash FROM users WHERE user_id = $1',
            [req.userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({
                message: 'User not found'
            });
        }

        const currentPasswordHash = userResult.rows[0].password_hash;

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, currentPasswordHash);
        if (!isCurrentPasswordValid) {
            return res.status(401).json({
                message: 'Current password is incorrect'
            });
        }

        // Hash new password
        const saltRounds = 12;
        const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await query(
            'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE user_id = $2',
            [newPasswordHash, req.userId]
        );

        res.json({
            message: 'Password changed successfully'
        });

    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            message: 'Internal server error while changing password'
        });
    }
});

// Delete account
router.delete('/account', authenticateToken, [
    body('password').notEmpty().withMessage('Password is required to delete account'),
    body('confirmDelete').equals('DELETE').withMessage('Please type DELETE to confirm account deletion')
], async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { password } = req.body;

        // Get user data
        const userResult = await query(
            'SELECT password_hash FROM users WHERE user_id = $1',
            [req.userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({
                message: 'User not found'
            });
        }

        const passwordHash = userResult.rows[0].password_hash;

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, passwordHash);
        if (!isPasswordValid) {
            return res.status(401).json({
                message: 'Password is incorrect'
            });
        }

        // Soft delete - deactivate account instead of hard delete
        await query(
            'UPDATE users SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE user_id = $1',
            [req.userId]
        );

        res.json({
            message: 'Account deleted successfully'
        });

    } catch (error) {
        console.error('Delete account error:', error);
        res.status(500).json({
            message: 'Internal server error while deleting account'
        });
    }
});

// Get user preferences (for future features)
router.get('/preferences', authenticateToken, async (req, res) => {
    try {
        // For now, return default preferences
        // In the future, this could be stored in a separate table
        const preferences = {
            language: 'en',
            theme: 'light',
            notifications: {
                email: true,
                practice_reminders: true,
                weekly_summary: true
            },
            practice: {
                session_size: 12,
                auto_play_audio: true,
                show_ipa: true,
                difficulty_adjustment: true
            }
        };

        res.json({
            preferences
        });

    } catch (error) {
        console.error('Get preferences error:', error);
        res.status(500).json({
            message: 'Internal server error while fetching preferences'
        });
    }
});

module.exports = router;
