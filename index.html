<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Russian Flashcards - Learn Russian with Spaced Repetition</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .star-animation {
            animation: starPulse 0.6s ease-in-out;
        }
        
        @keyframes starPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }
        
        .shake {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div id="root"></div>
    
    <script type="text/babel">
        // Main App Component will be loaded here
        const { useState, useEffect, useContext, createContext } = React;
        
        // Global state context
        const AppContext = createContext();
        
        // API base URL
        const API_BASE = window.location.origin + '/api';
        
        // Utility functions
        const api = {
            async request(endpoint, options = {}) {
                const token = localStorage.getItem('token');
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(token && { Authorization: `Bearer ${token}` }),
                        ...options.headers
                    },
                    ...options
                };

                try {
                    const response = await fetch(`${API_BASE}${endpoint}`, config);
                    const data = await response.json();

                    if (!response.ok) {
                        // Create an error object that preserves the full response data
                        const error = new Error(data.message || 'Request failed');
                        error.errors = data.errors; // Preserve validation errors
                        error.status = response.status;
                        throw error;
                    }

                    return data;
                } catch (error) {
                    console.error('API Error:', error);
                    throw error;
                }
            },
            
            get(endpoint) {
                return this.request(endpoint);
            },
            
            post(endpoint, data) {
                return this.request(endpoint, {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
            },
            
            put(endpoint, data) {
                return this.request(endpoint, {
                    method: 'PUT',
                    body: JSON.stringify(data)
                });
            },
            
            delete(endpoint) {
                return this.request(endpoint, {
                    method: 'DELETE'
                });
            }
        };
        
        // App component placeholder - will be expanded
        function App() {
            const [user, setUser] = useState(null);
            const [loading, setLoading] = useState(true);
            const [currentPage, setCurrentPage] = useState('home');
            
            useEffect(() => {
                // Check for existing token on app load
                const token = localStorage.getItem('token');
                if (token) {
                    // Verify token and get user info
                    api.get('/auth/verify')
                        .then(userData => {
                            setUser(userData);
                            setCurrentPage('dashboard');
                        })
                        .catch(() => {
                            localStorage.removeItem('token');
                        })
                        .finally(() => setLoading(false));
                } else {
                    setLoading(false);
                }
            }, []);
            
            if (loading) {
                return (
                    <div className="flex items-center justify-center min-h-screen">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }
            
            return (
                <AppContext.Provider value={{ user, setUser, currentPage, setCurrentPage, api }}>
                    <div className="min-h-screen bg-gray-50">
                        <Navigation />
                        <main className="container mx-auto px-4 py-8">
                            {currentPage === 'home' && <HomePage />}
                            {currentPage === 'login' && <LoginPage />}
                            {currentPage === 'register' && <RegisterPage />}
                            {currentPage === 'dashboard' && <Dashboard />}
                            {currentPage === 'create-card' && <CreateCardPage />}
                            {currentPage === 'practice' && <PracticePage />}
                            {currentPage === 'profile' && <ProfilePage />}
                        </main>
                    </div>
                </AppContext.Provider>
            );
        }
        
        // Navigation component
        function Navigation() {
            const { user, setUser, currentPage, setCurrentPage, api } = useContext(AppContext);

            const handleLogout = async () => {
                try {
                    await api.post('/auth/logout');
                } catch (error) {
                    console.error('Logout error:', error);
                } finally {
                    localStorage.removeItem('token');
                    setUser(null);
                    setCurrentPage('home');
                }
            };

            return (
                <nav className="bg-blue-600 text-white p-4">
                    <div className="container mx-auto flex justify-between items-center">
                        <button
                            onClick={() => setCurrentPage(user ? 'dashboard' : 'home')}
                            className="text-xl font-bold hover:text-blue-200 transition-colors"
                        >
                            Russian Flashcards
                        </button>

                        <div className="flex items-center space-x-4">
                            {user ? (
                                <>
                                    <button
                                        onClick={() => setCurrentPage('dashboard')}
                                        className={`px-3 py-2 rounded transition-colors ${
                                            currentPage === 'dashboard' ? 'bg-blue-700' : 'hover:bg-blue-700'
                                        }`}
                                    >
                                        Dashboard
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage('practice')}
                                        className={`px-3 py-2 rounded transition-colors ${
                                            currentPage === 'practice' ? 'bg-blue-700' : 'hover:bg-blue-700'
                                        }`}
                                    >
                                        Practice
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage('create-card')}
                                        className={`px-3 py-2 rounded transition-colors ${
                                            currentPage === 'create-card' ? 'bg-blue-700' : 'hover:bg-blue-700'
                                        }`}
                                    >
                                        Add Card
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage('profile')}
                                        className={`px-3 py-2 rounded transition-colors ${
                                            currentPage === 'profile' ? 'bg-blue-700' : 'hover:bg-blue-700'
                                        }`}
                                    >
                                        Profile
                                    </button>
                                    <button
                                        onClick={handleLogout}
                                        className="px-3 py-2 rounded hover:bg-blue-700 transition-colors"
                                    >
                                        Logout
                                    </button>
                                </>
                            ) : (
                                <>
                                    <button
                                        onClick={() => setCurrentPage('login')}
                                        className="px-3 py-2 rounded hover:bg-blue-700 transition-colors"
                                    >
                                        Sign In
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage('register')}
                                        className="bg-blue-700 px-3 py-2 rounded hover:bg-blue-800 transition-colors"
                                    >
                                        Sign Up
                                    </button>
                                </>
                            )}
                        </div>
                    </div>
                </nav>
            );
        }
        
        // HomePage Component
        function HomePage() {
            const { user, setCurrentPage } = useContext(AppContext);

            if (user) {
                setCurrentPage('dashboard');
                return null;
            }

            return (
                <div className="max-w-4xl mx-auto text-center">
                    <div className="mb-12">
                        <h1 className="text-5xl font-bold text-gray-900 mb-6">
                            Learn Russian with <span className="text-blue-600">Smart Flashcards</span>
                        </h1>
                        <p className="text-xl text-gray-600 mb-8">
                            Master Russian vocabulary using scientifically-proven spaced repetition
                        </p>
                        <div className="space-x-4">
                            <button
                                onClick={() => setCurrentPage('register')}
                                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                            >
                                Get Started Free
                            </button>
                            <button
                                onClick={() => setCurrentPage('login')}
                                className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                            >
                                Sign In
                            </button>
                        </div>
                    </div>

                    <div className="grid md:grid-cols-3 gap-8 mb-12">
                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <div className="text-blue-600 text-3xl mb-4">
                                <i className="fas fa-brain"></i>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Spaced Repetition</h3>
                            <p className="text-gray-600">
                                Review words at optimal intervals to maximize retention and minimize study time.
                            </p>
                        </div>

                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <div className="text-blue-600 text-3xl mb-4">
                                <i className="fas fa-volume-up"></i>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Audio Pronunciation</h3>
                            <p className="text-gray-600">
                                Hear native Russian pronunciation for every word to improve your speaking skills.
                            </p>
                        </div>

                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <div className="text-blue-600 text-3xl mb-4">
                                <i className="fas fa-search"></i>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Smart Search</h3>
                            <p className="text-gray-600">
                                Instantly find words in our database or create custom flashcards with ease.
                            </p>
                        </div>
                    </div>
                </div>
            );
        }

        // LoginPage Component
        function LoginPage() {
            const { setUser, setCurrentPage, api } = useContext(AppContext);
            const [formData, setFormData] = useState({ email: '', password: '' });
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');

                try {
                    const response = await api.post('/auth/login', formData);
                    localStorage.setItem('token', response.token);
                    setUser(response.user);
                    setCurrentPage('dashboard');
                } catch (error) {
                    console.error('Login error:', error);
                    // Handle validation errors with detailed messages
                    if (error.errors && Array.isArray(error.errors)) {
                        const errorMessages = error.errors
                            .filter(err => err && err.msg) // Filter out null/undefined errors
                            .map(err => err.msg)
                            .join('. ');
                        setError(errorMessages || 'Validation failed');
                    } else {
                        setError(error.message || 'Login failed');
                    }
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
                    <h2 className="text-2xl font-bold text-center mb-6">Sign In</h2>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input
                                type="email"
                                value={formData.email}
                                onChange={(e) => setFormData({...formData, email: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input
                                type="password"
                                value={formData.password}
                                onChange={(e) => setFormData({...formData, password: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <button
                            onClick={handleSubmit}
                            disabled={loading}
                            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                        >
                            {loading ? 'Signing In...' : 'Sign In'}
                        </button>
                    </div>

                    <div className="text-center mt-4">
                        <span className="text-gray-600">Don't have an account? </span>
                        <button
                            onClick={() => setCurrentPage('register')}
                            className="text-blue-600 hover:underline"
                        >
                            Sign up
                        </button>
                    </div>
                </div>
            );
        }
        
        // RegisterPage Component
        function RegisterPage() {
            const { setUser, setCurrentPage, api } = useContext(AppContext);
            const [formData, setFormData] = useState({
                email: '',
                password: '',
                confirmPassword: '',
                firstName: '',
                lastName: ''
            });
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');

                if (formData.password !== formData.confirmPassword) {
                    setError('Passwords do not match');
                    setLoading(false);
                    return;
                }

                try {
                    const response = await api.post('/auth/register', {
                        email: formData.email,
                        password: formData.password,
                        firstName: formData.firstName,
                        lastName: formData.lastName
                    });
                    localStorage.setItem('token', response.token);
                    setUser(response.user);
                    setCurrentPage('dashboard');
                } catch (error) {
                    console.error('Registration error:', error);
                    // Handle validation errors with detailed messages
                    if (error.errors && Array.isArray(error.errors)) {
                        const errorMessages = error.errors
                            .filter(err => err && err.msg) // Filter out null/undefined errors
                            .map(err => err.msg)
                            .join('. ');
                        setError(errorMessages || 'Validation failed');
                    } else {
                        setError(error.message || 'Registration failed');
                    }
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
                    <h2 className="text-2xl font-bold text-center mb-6">Create Account</h2>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                <input
                                    type="text"
                                    value={formData.firstName}
                                    onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                <input
                                    type="text"
                                    value={formData.lastName}
                                    onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input
                                type="email"
                                value={formData.email}
                                onChange={(e) => setFormData({...formData, email: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input
                                type="password"
                                value={formData.password}
                                onChange={(e) => setFormData({...formData, password: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                            <input
                                type="password"
                                value={formData.confirmPassword}
                                onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <button
                            onClick={handleSubmit}
                            disabled={loading}
                            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                        >
                            {loading ? 'Creating Account...' : 'Create Account'}
                        </button>
                    </div>

                    <div className="text-center mt-4">
                        <span className="text-gray-600">Already have an account? </span>
                        <button
                            onClick={() => setCurrentPage('login')}
                            className="text-blue-600 hover:underline"
                        >
                            Sign in
                        </button>
                    </div>
                </div>
            );
        }

        // Dashboard Component
        function Dashboard() {
            const { user, setCurrentPage, api } = useContext(AppContext);
            const [flashcards, setFlashcards] = useState([]);
            const [stats, setStats] = useState(null);
            const [loading, setLoading] = useState(true);
            const [searchTerm, setSearchTerm] = useState('');

            useEffect(() => {
                loadDashboardData();
            }, []);

            const loadDashboardData = async () => {
                try {
                    const [flashcardsResponse, statsResponse] = await Promise.all([
                        api.get('/flashcards?limit=10'),
                        api.get('/practice/stats')
                    ]);

                    setFlashcards(flashcardsResponse.flashcards || []);
                    setStats(statsResponse);
                } catch (error) {
                    console.error('Error loading dashboard data:', error);
                } finally {
                    setLoading(false);
                }
            };

            const startPractice = () => {
                setCurrentPage('practice');
            };

            if (loading) {
                return (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }

            return (
                <div className="max-w-6xl mx-auto">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Welcome back, {user?.firstName || 'Student'}!
                        </h1>
                        <p className="text-gray-600">Ready to continue your Russian learning journey?</p>
                    </div>

                    {/* Stats Cards */}
                    {stats && (
                        <div className="grid md:grid-cols-4 gap-6 mb-8">
                            <div className="bg-white p-6 rounded-lg shadow-md">
                                <div className="flex items-center">
                                    <div className="text-blue-600 text-2xl mr-3">
                                        <i className="fas fa-layer-group"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Cards</p>
                                        <p className="text-2xl font-bold">{stats.cards?.totalCards || 0}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white p-6 rounded-lg shadow-md">
                                <div className="flex items-center">
                                    <div className="text-orange-600 text-2xl mr-3">
                                        <i className="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Due for Review</p>
                                        <p className="text-2xl font-bold">{stats.cards?.dueForReview || 0}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white p-6 rounded-lg shadow-md">
                                <div className="flex items-center">
                                    <div className="text-green-600 text-2xl mr-3">
                                        <i className="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Accuracy</p>
                                        <p className="text-2xl font-bold">{stats.sessions?.accuracy || 0}%</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white p-6 rounded-lg shadow-md">
                                <div className="flex items-center">
                                    <div className="text-purple-600 text-2xl mr-3">
                                        <i className="fas fa-fire"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Study Streak</p>
                                        <p className="text-2xl font-bold">{stats.streak?.studyDays || 0} days</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex flex-wrap gap-4 mb-8">
                        <button
                            onClick={startPractice}
                            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center"
                        >
                            <i className="fas fa-play mr-2"></i>
                            Start Practice Session
                        </button>

                        <button
                            onClick={() => setCurrentPage('create-card')}
                            className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center"
                        >
                            <i className="fas fa-plus mr-2"></i>
                            Add New Card
                        </button>
                    </div>

                    {/* Recent Cards */}
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl font-semibold">Your Flashcards</h2>
                            <div className="flex items-center space-x-4">
                                <input
                                    type="text"
                                    placeholder="Search cards..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                        </div>

                        {flashcards.length > 0 ? (
                            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {flashcards.map(card => (
                                    <div key={card.card_id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div className="flex justify-between items-start mb-2">
                                            <h3 className="font-semibold text-lg">{card.russian_word}</h3>
                                            {card.audio_url && (
                                                <button className="text-blue-600 hover:text-blue-800">
                                                    <i className="fas fa-volume-up"></i>
                                                </button>
                                            )}
                                        </div>
                                        <p className="text-gray-600 mb-2">
                                            {Array.isArray(card.english_translations)
                                                ? card.english_translations.join(', ')
                                                : card.english_translations}
                                        </p>
                                        {card.ipa_transcription && (
                                            <p className="text-sm text-gray-500 italic">[{card.ipa_transcription}]</p>
                                        )}
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <div className="text-gray-400 text-4xl mb-4">
                                    <i className="fas fa-layer-group"></i>
                                </div>
                                <p className="text-gray-600 mb-4">You don't have any flashcards yet.</p>
                                <button
                                    onClick={() => setCurrentPage('create-card')}
                                    className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    Create Your First Card
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        // CreateCardPage Component
        function CreateCardPage() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [formData, setFormData] = useState({
                russianWord: '',
                englishTranslations: [''],
                ipaTranscription: '',
                exampleSentence: '',
                imageUrl: '',
                difficultyLevel: 1,
                tags: []
            });
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');
            const [success, setSuccess] = useState('');
            const [searchResults, setSearchResults] = useState([]);
            const [searching, setSearching] = useState(false);

            const searchGlobalDatabase = async (word) => {
                if (!word || word.length < 2) {
                    setSearchResults([]);
                    return;
                }

                setSearching(true);
                try {
                    const response = await api.get(`/flashcards/search-global?q=${encodeURIComponent(word)}`);
                    setSearchResults(response.results || []);
                } catch (error) {
                    console.error('Search error:', error);
                    setSearchResults([]);
                } finally {
                    setSearching(false);
                }
            };

            const handleWordChange = (word) => {
                setFormData({...formData, russianWord: word});
                searchGlobalDatabase(word);
            };

            const selectFromGlobal = (globalCard) => {
                setFormData({
                    ...formData,
                    russianWord: globalCard.russian_word,
                    englishTranslations: globalCard.english_translations,
                    ipaTranscription: globalCard.ipa_transcription || '',
                    exampleSentence: globalCard.example_sentences?.[0] || ''
                });
                setSearchResults([]);
            };

            const addTranslation = () => {
                setFormData({
                    ...formData,
                    englishTranslations: [...formData.englishTranslations, '']
                });
            };

            const removeTranslation = (index) => {
                const newTranslations = formData.englishTranslations.filter((_, i) => i !== index);
                setFormData({...formData, englishTranslations: newTranslations});
            };

            const updateTranslation = (index, value) => {
                const newTranslations = [...formData.englishTranslations];
                newTranslations[index] = value;
                setFormData({...formData, englishTranslations: newTranslations});
            };

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');
                setSuccess('');

                // Filter out empty translations
                const validTranslations = formData.englishTranslations.filter(t => t.trim());

                if (validTranslations.length === 0) {
                    setError('At least one English translation is required');
                    setLoading(false);
                    return;
                }

                try {
                    await api.post('/flashcards', {
                        ...formData,
                        englishTranslations: validTranslations
                    });

                    setSuccess('Flashcard created successfully!');
                    setFormData({
                        russianWord: '',
                        englishTranslations: [''],
                        ipaTranscription: '',
                        exampleSentence: '',
                        imageUrl: '',
                        difficultyLevel: 1,
                        tags: []
                    });

                    setTimeout(() => {
                        setCurrentPage('dashboard');
                    }, 2000);

                } catch (error) {
                    setError(error.message || 'Failed to create flashcard');
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8">
                    <h2 className="text-2xl font-bold text-center mb-6">Create New Flashcard</h2>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    {success && (
                        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {success}
                        </div>
                    )}

                    <div className="space-y-6">
                        {/* Russian Word */}
                        <div className="relative">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Russian Word *
                            </label>
                            <input
                                type="text"
                                value={formData.russianWord}
                                onChange={(e) => handleWordChange(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Enter Russian word..."
                                required
                            />

                            {/* Search Results */}
                            {searchResults.length > 0 && (
                                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                                    {searchResults.map((result, index) => (
                                        <button
                                            key={index}
                                            onClick={() => selectFromGlobal(result)}
                                            className="w-full text-left px-4 py-2 hover:bg-gray-100 border-b border-gray-200 last:border-b-0"
                                        >
                                            <div className="font-medium">{result.russian_word}</div>
                                            <div className="text-sm text-gray-600">
                                                {Array.isArray(result.english_translations)
                                                    ? result.english_translations.join(', ')
                                                    : result.english_translations}
                                            </div>
                                        </button>
                                    ))}
                                </div>
                            )}

                            {searching && (
                                <div className="absolute right-3 top-9">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                </div>
                            )}
                        </div>

                        {/* English Translations */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                English Translations *
                            </label>
                            {formData.englishTranslations.map((translation, index) => (
                                <div key={index} className="flex items-center space-x-2 mb-2">
                                    <input
                                        type="text"
                                        value={translation}
                                        onChange={(e) => updateTranslation(index, e.target.value)}
                                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="English translation..."
                                        required={index === 0}
                                    />
                                    {formData.englishTranslations.length > 1 && (
                                        <button
                                            onClick={() => removeTranslation(index)}
                                            className="text-red-600 hover:text-red-800"
                                        >
                                            <i className="fas fa-times"></i>
                                        </button>
                                    )}
                                </div>
                            ))}
                            <button
                                onClick={addTranslation}
                                className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                                + Add another translation
                            </button>
                        </div>

                        {/* IPA Transcription */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                IPA Transcription (optional)
                            </label>
                            <input
                                type="text"
                                value={formData.ipaTranscription}
                                onChange={(e) => setFormData({...formData, ipaTranscription: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="[pronunciation]"
                            />
                        </div>

                        {/* Example Sentence */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Example Sentence (optional)
                            </label>
                            <textarea
                                value={formData.exampleSentence}
                                onChange={(e) => setFormData({...formData, exampleSentence: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                rows="2"
                                maxLength="250"
                                placeholder="Example sentence in Russian..."
                            />
                            <div className="text-sm text-gray-500 mt-1">
                                {formData.exampleSentence.length}/250 characters
                            </div>
                        </div>

                        {/* Difficulty Level */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Difficulty Level
                            </label>
                            <select
                                value={formData.difficultyLevel}
                                onChange={(e) => setFormData({...formData, difficultyLevel: parseInt(e.target.value)})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value={1}>1 - Very Easy</option>
                                <option value={2}>2 - Easy</option>
                                <option value={3}>3 - Medium</option>
                                <option value={4}>4 - Hard</option>
                                <option value={5}>5 - Very Hard</option>
                            </select>
                        </div>

                        {/* Submit Button */}
                        <div className="flex space-x-4">
                            <button
                                onClick={handleSubmit}
                                disabled={loading}
                                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                            >
                                {loading ? 'Creating...' : 'Create Flashcard'}
                            </button>

                            <button
                                onClick={() => setCurrentPage('dashboard')}
                                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            );
        }

        // PracticePage Component
        function PracticePage() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [session, setSession] = useState(null);
            const [currentCardIndex, setCurrentCardIndex] = useState(0);
            const [userAnswer, setUserAnswer] = useState('');
            const [showResult, setShowResult] = useState(false);
            const [isCorrect, setIsCorrect] = useState(false);
            const [sessionStats, setSessionStats] = useState({ correct: 0, incorrect: 0 });
            const [loading, setLoading] = useState(true);
            const [sessionComplete, setSessionComplete] = useState(false);
            const [incorrectCards, setIncorrectCards] = useState([]);

            useEffect(() => {
                loadPracticeSession();
            }, []);

            const loadPracticeSession = async () => {
                try {
                    const response = await api.get('/practice/session');
                    setSession(response);
                    setLoading(false);
                } catch (error) {
                    console.error('Error loading practice session:', error);
                    setLoading(false);
                }
            };

            const playAudio = (audioUrl) => {
                if (audioUrl) {
                    const audio = new Audio(audioUrl);
                    audio.play().catch(error => console.error('Audio play error:', error));
                }
            };

            const checkAnswer = () => {
                if (!session || !userAnswer.trim()) return;

                const currentCard = session.cards[currentCardIndex];
                const correctTranslations = currentCard.englishTranslations;
                const userAnswerLower = userAnswer.trim().toLowerCase();

                // Check if user answer matches any of the correct translations
                const correct = correctTranslations.some(translation =>
                    translation.toLowerCase() === userAnswerLower ||
                    translation.toLowerCase().includes(userAnswerLower) ||
                    userAnswerLower.includes(translation.toLowerCase())
                );

                setIsCorrect(correct);
                setShowResult(true);

                // Update session stats
                if (correct) {
                    setSessionStats(prev => ({ ...prev, correct: prev.correct + 1 }));
                } else {
                    setSessionStats(prev => ({ ...prev, incorrect: prev.incorrect + 1 }));
                    setIncorrectCards(prev => [...prev, currentCard]);
                }

                // Submit answer to backend
                submitAnswer(currentCard.cardId, correct);
            };

            const submitAnswer = async (cardId, correct) => {
                try {
                    await api.post('/practice/answer', {
                        cardId,
                        sessionId: session.sessionId,
                        userAnswer: userAnswer.trim(),
                        isCorrect: correct,
                        responseTime: 0 // Could implement timing later
                    });
                } catch (error) {
                    console.error('Error submitting answer:', error);
                }
            };

            const nextCard = () => {
                if (currentCardIndex < session.cards.length - 1) {
                    setCurrentCardIndex(currentCardIndex + 1);
                    setUserAnswer('');
                    setShowResult(false);
                } else {
                    // Session complete
                    setSessionComplete(true);
                }
            };

            const restartSession = () => {
                setCurrentCardIndex(0);
                setUserAnswer('');
                setShowResult(false);
                setSessionStats({ correct: 0, incorrect: 0 });
                setSessionComplete(false);
                setIncorrectCards([]);
                loadPracticeSession();
            };

            if (loading) {
                return (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }

            if (!session || session.cards.length === 0) {
                return (
                    <div className="max-w-2xl mx-auto text-center">
                        <div className="bg-white rounded-lg shadow-md p-8">
                            <div className="text-gray-400 text-4xl mb-4">
                                <i className="fas fa-layer-group"></i>
                            </div>
                            <h2 className="text-2xl font-bold mb-4">No Cards Available</h2>
                            <p className="text-gray-600 mb-6">
                                You don't have any cards ready for practice. Create some flashcards first!
                            </p>
                            <button
                                onClick={() => setCurrentPage('create-card')}
                                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                            >
                                Create Your First Card
                            </button>
                        </div>
                    </div>
                );
            }

            if (sessionComplete) {
                const accuracy = Math.round((sessionStats.correct / (sessionStats.correct + sessionStats.incorrect)) * 100);

                return (
                    <div className="max-w-2xl mx-auto">
                        <div className="bg-white rounded-lg shadow-md p-8 text-center">
                            <div className="text-green-600 text-4xl mb-4">
                                <i className="fas fa-check-circle"></i>
                            </div>
                            <h2 className="text-2xl font-bold mb-4">Session Complete!</h2>

                            <div className="grid grid-cols-3 gap-4 mb-6">
                                <div className="bg-green-100 p-4 rounded-lg">
                                    <div className="text-2xl font-bold text-green-600">{sessionStats.correct}</div>
                                    <div className="text-sm text-gray-600">Correct</div>
                                </div>
                                <div className="bg-red-100 p-4 rounded-lg">
                                    <div className="text-2xl font-bold text-red-600">{sessionStats.incorrect}</div>
                                    <div className="text-sm text-gray-600">Incorrect</div>
                                </div>
                                <div className="bg-blue-100 p-4 rounded-lg">
                                    <div className="text-2xl font-bold text-blue-600">{accuracy}%</div>
                                    <div className="text-sm text-gray-600">Accuracy</div>
                                </div>
                            </div>

                            {incorrectCards.length > 0 && (
                                <div className="mb-6">
                                    <h3 className="text-lg font-semibold mb-3">Review These Cards:</h3>
                                    <div className="space-y-2">
                                        {incorrectCards.map((card, index) => (
                                            <div key={index} className="bg-gray-100 p-3 rounded text-left">
                                                <div className="font-medium">{card.russianWord}</div>
                                                <div className="text-sm text-gray-600">
                                                    {Array.isArray(card.englishTranslations)
                                                        ? card.englishTranslations.join(', ')
                                                        : card.englishTranslations}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            <div className="space-x-4">
                                <button
                                    onClick={restartSession}
                                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    Practice Again
                                </button>
                                <button
                                    onClick={() => setCurrentPage('dashboard')}
                                    className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors"
                                >
                                    Back to Dashboard
                                </button>
                            </div>
                        </div>
                    </div>
                );
            }

            const currentCard = session.cards[currentCardIndex];
            const progress = ((currentCardIndex + 1) / session.cards.length) * 100;

            return (
                <div className="max-w-2xl mx-auto">
                    {/* Progress Bar */}
                    <div className="mb-6">
                        <div className="flex justify-between text-sm text-gray-600 mb-2">
                            <span>Card {currentCardIndex + 1} of {session.cards.length}</span>
                            <span>{sessionStats.correct} correct, {sessionStats.incorrect} incorrect</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${progress}%` }}
                            ></div>
                        </div>
                    </div>

                    {/* Practice Card */}
                    <div className="bg-white rounded-lg shadow-md p-8">
                        <div className="text-center mb-8">
                            <div className="flex justify-center items-center space-x-4 mb-4">
                                <h2 className="text-4xl font-bold text-gray-900">
                                    {currentCard.russianWord}
                                </h2>
                                {currentCard.audioUrl && (
                                    <button
                                        onClick={() => playAudio(currentCard.audioUrl)}
                                        className="text-blue-600 hover:text-blue-800 text-2xl"
                                    >
                                        <i className="fas fa-volume-up"></i>
                                    </button>
                                )}
                            </div>

                            {currentCard.ipaTranscription && (
                                <p className="text-lg text-gray-500 italic mb-4">
                                    [{currentCard.ipaTranscription}]
                                </p>
                            )}

                            {currentCard.exampleSentence && (
                                <p className="text-gray-600 italic mb-6">
                                    "{currentCard.exampleSentence}"
                                </p>
                            )}
                        </div>

                        {!showResult ? (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Type the English translation:
                                    </label>
                                    <input
                                        type="text"
                                        value={userAnswer}
                                        onChange={(e) => setUserAnswer(e.target.value)}
                                        onKeyPress={(e) => e.key === 'Enter' && checkAnswer()}
                                        className="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Your answer..."
                                        autoFocus
                                    />
                                </div>

                                <button
                                    onClick={checkAnswer}
                                    disabled={!userAnswer.trim()}
                                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                                >
                                    Check Answer
                                </button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                <div className={`p-4 rounded-lg ${isCorrect ? 'bg-green-100 border border-green-400' : 'bg-red-100 border border-red-400'}`}>
                                    <div className="flex items-center space-x-2 mb-2">
                                        <div className={`text-2xl ${isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                                            <i className={`fas ${isCorrect ? 'fa-star star-animation' : 'fa-times shake'}`}></i>
                                        </div>
                                        <span className={`font-semibold ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>
                                            {isCorrect ? 'Correct!' : 'Incorrect'}
                                        </span>
                                    </div>

                                    <div className="text-sm">
                                        <p><strong>Your answer:</strong> {userAnswer}</p>
                                        <p><strong>Correct answers:</strong> {
                                            Array.isArray(currentCard.englishTranslations)
                                                ? currentCard.englishTranslations.join(', ')
                                                : currentCard.englishTranslations
                                        }</p>
                                    </div>
                                </div>

                                <button
                                    onClick={nextCard}
                                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors"
                                >
                                    {currentCardIndex < session.cards.length - 1 ? 'Next Card' : 'Finish Session'}
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        // ProfilePage Component
        function ProfilePage() {
            const { user, setUser, api } = useContext(AppContext);
            const [profile, setProfile] = useState(null);
            const [editing, setEditing] = useState(false);
            const [formData, setFormData] = useState({
                firstName: '',
                lastName: '',
                email: ''
            });
            const [passwordData, setPasswordData] = useState({
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
            });
            const [loading, setLoading] = useState(true);
            const [saving, setSaving] = useState(false);
            const [error, setError] = useState('');
            const [success, setSuccess] = useState('');
            const [showPasswordForm, setShowPasswordForm] = useState(false);

            useEffect(() => {
                loadProfile();
            }, []);

            const loadProfile = async () => {
                try {
                    const response = await api.get('/users/profile');
                    setProfile(response);
                    setFormData({
                        firstName: response.user.firstName || '',
                        lastName: response.user.lastName || '',
                        email: response.user.email || ''
                    });
                } catch (error) {
                    setError('Failed to load profile');
                } finally {
                    setLoading(false);
                }
            };

            const handleSaveProfile = async () => {
                setSaving(true);
                setError('');
                setSuccess('');

                try {
                    const response = await api.put('/users/profile', formData);
                    setProfile(prev => ({ ...prev, user: response.user }));
                    setUser(response.user);
                    setEditing(false);
                    setSuccess('Profile updated successfully!');
                } catch (error) {
                    setError(error.message || 'Failed to update profile');
                } finally {
                    setSaving(false);
                }
            };

            const handleChangePassword = async () => {
                if (passwordData.newPassword !== passwordData.confirmPassword) {
                    setError('New passwords do not match');
                    return;
                }

                setSaving(true);
                setError('');
                setSuccess('');

                try {
                    await api.put('/users/password', passwordData);
                    setPasswordData({
                        currentPassword: '',
                        newPassword: '',
                        confirmPassword: ''
                    });
                    setShowPasswordForm(false);
                    setSuccess('Password changed successfully!');
                } catch (error) {
                    setError(error.message || 'Failed to change password');
                } finally {
                    setSaving(false);
                }
            };

            if (loading) {
                return (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }

            return (
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-3xl font-bold text-gray-900 mb-8">Profile Settings</h1>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    {success && (
                        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {success}
                        </div>
                    )}

                    <div className="grid md:grid-cols-2 gap-8">
                        {/* Profile Information */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-xl font-semibold">Personal Information</h2>
                                {!editing && (
                                    <button
                                        onClick={() => setEditing(true)}
                                        className="text-blue-600 hover:text-blue-800"
                                    >
                                        <i className="fas fa-edit mr-1"></i>
                                        Edit
                                    </button>
                                )}
                            </div>

                            {editing ? (
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            First Name
                                        </label>
                                        <input
                                            type="text"
                                            value={formData.firstName}
                                            onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Last Name
                                        </label>
                                        <input
                                            type="text"
                                            value={formData.lastName}
                                            onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Email
                                        </label>
                                        <input
                                            type="email"
                                            value={formData.email}
                                            onChange={(e) => setFormData({...formData, email: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div className="flex space-x-3">
                                        <button
                                            onClick={handleSaveProfile}
                                            disabled={saving}
                                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                                        >
                                            {saving ? 'Saving...' : 'Save Changes'}
                                        </button>
                                        <button
                                            onClick={() => {
                                                setEditing(false);
                                                setFormData({
                                                    firstName: profile.user.firstName || '',
                                                    lastName: profile.user.lastName || '',
                                                    email: profile.user.email || ''
                                                });
                                            }}
                                            className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">First Name</label>
                                        <p className="text-gray-900">{profile?.user.firstName || 'Not set'}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Last Name</label>
                                        <p className="text-gray-900">{profile?.user.lastName || 'Not set'}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Email</label>
                                        <p className="text-gray-900">{profile?.user.email}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Member Since</label>
                                        <p className="text-gray-900">
                                            {profile?.user.createdAt ? new Date(profile.user.createdAt).toLocaleDateString() : 'Unknown'}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Statistics */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h2 className="text-xl font-semibold mb-6">Learning Statistics</h2>

                            {profile?.statistics && (
                                <div className="space-y-4">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Total Flashcards</span>
                                        <span className="font-semibold">{profile.statistics.totalCards}</span>
                                    </div>

                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Cards Due for Review</span>
                                        <span className="font-semibold text-orange-600">{profile.statistics.dueCards}</span>
                                    </div>

                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Average Accuracy</span>
                                        <span className="font-semibold text-green-600">{profile.statistics.averageAccuracy}%</span>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Security Settings */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h2 className="text-xl font-semibold mb-6">Security</h2>

                            {!showPasswordForm ? (
                                <button
                                    onClick={() => setShowPasswordForm(true)}
                                    className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
                                >
                                    Change Password
                                </button>
                            ) : (
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Current Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.currentPassword}
                                            onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            New Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.newPassword}
                                            onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Confirm New Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.confirmPassword}
                                            onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div className="flex space-x-3">
                                        <button
                                            onClick={handleChangePassword}
                                            disabled={saving}
                                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                                        >
                                            {saving ? 'Changing...' : 'Change Password'}
                                        </button>
                                        <button
                                            onClick={() => {
                                                setShowPasswordForm(false);
                                                setPasswordData({
                                                    currentPassword: '',
                                                    newPassword: '',
                                                    confirmPassword: ''
                                                });
                                            }}
                                            className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        }

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
