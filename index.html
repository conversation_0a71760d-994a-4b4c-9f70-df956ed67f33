<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Russian Flashcards - Learn Russian with Spaced Repetition</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .star-animation {
            animation: starPulse 0.6s ease-in-out;
        }
        
        @keyframes starPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }
        
        .shake {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div id="root"></div>
    
    <script type="text/babel">
        // Main App Component will be loaded here
        const { useState, useEffect, useContext, createContext } = React;
        
        // Global state context
        const AppContext = createContext();
        
        // API base URL
        const API_BASE = window.location.origin + '/api';
        
        // Utility functions
        const api = {
            async request(endpoint, options = {}) {
                const token = localStorage.getItem('token');
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(token && { Authorization: `Bearer ${token}` }),
                        ...options.headers
                    },
                    ...options
                };

                try {
                    const response = await fetch(`${API_BASE}${endpoint}`, config);
                    const data = await response.json();

                    if (!response.ok) {
                        // Create an error object that preserves the full response data
                        const error = new Error(data.message || 'Request failed');
                        error.errors = data.errors; // Preserve validation errors
                        error.status = response.status;
                        throw error;
                    }

                    return data;
                } catch (error) {
                    console.error('API Error:', error);
                    throw error;
                }
            },
            
            get(endpoint) {
                return this.request(endpoint);
            },
            
            post(endpoint, data) {
                return this.request(endpoint, {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
            },
            
            put(endpoint, data) {
                return this.request(endpoint, {
                    method: 'PUT',
                    body: JSON.stringify(data)
                });
            },
            
            delete(endpoint) {
                return this.request(endpoint, {
                    method: 'DELETE'
                });
            }
        };
        
        // App component placeholder - will be expanded
        function App() {
            const [user, setUser] = useState(null);
            const [loading, setLoading] = useState(true);
            const [currentPage, setCurrentPage] = useState('home');
            
            useEffect(() => {
                // Check for existing token on app load
                const token = localStorage.getItem('token');
                if (token) {
                    // Verify token and get user info
                    api.get('/auth/verify')
                        .then(userData => {
                            setUser(userData);
                            // Redirect admins to admin dashboard, regular users to user dashboard
                            setCurrentPage(userData.isAdmin ? 'admin-dashboard' : 'dashboard');
                        })
                        .catch(() => {
                            localStorage.removeItem('token');
                        })
                        .finally(() => setLoading(false));
                } else {
                    setLoading(false);
                }
            }, []);
            
            if (loading) {
                return (
                    <div className="flex items-center justify-center min-h-screen">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }
            
            return (
                <AppContext.Provider value={{ user, setUser, currentPage, setCurrentPage, api }}>
                    <div className="min-h-screen bg-gray-50">
                        <Navigation />
                        <main className="container mx-auto px-4 py-8">
                            {currentPage === 'home' && <HomePage />}
                            {currentPage === 'login' && <LoginPage />}
                            {currentPage === 'register' && <RegisterPage />}
                            {currentPage === 'dashboard' && <Dashboard />}
                            {currentPage === 'request-word' && <WordRequestPage />}
                            {currentPage === 'practice' && <PracticePage />}
                            {currentPage === 'profile' && <ProfilePage />}
                            {currentPage === 'admin-dashboard' && user?.isAdmin && <AdminDashboard />}
                            {currentPage === 'admin-requests' && user?.isAdmin && <AdminRequestsPage />}
                            {currentPage === 'admin-cards' && user?.isAdmin && <AdminCardsPage />}
                            {currentPage === 'admin-users' && user?.isAdmin && <AdminUsersPage />}
                        </main>
                    </div>
                </AppContext.Provider>
            );
        }
        
        // Navigation component
        function Navigation() {
            const { user, setUser, currentPage, setCurrentPage, api } = useContext(AppContext);

            const handleLogout = async () => {
                try {
                    await api.post('/auth/logout');
                } catch (error) {
                    console.error('Logout error:', error);
                } finally {
                    localStorage.removeItem('token');
                    setUser(null);
                    setCurrentPage('home');
                }
            };

            return (
                <nav className="bg-blue-600 text-white p-4">
                    <div className="container mx-auto flex justify-between items-center">
                        <button
                            onClick={() => setCurrentPage(user ? (user.isAdmin ? 'admin-dashboard' : 'dashboard') : 'home')}
                            className="text-xl font-bold hover:text-blue-200 transition-colors"
                        >
                            Russian Flashcards
                        </button>

                        <div className="flex items-center space-x-4">
                            {user ? (
                                <>
                                    {user.isAdmin ? (
                                        // Admin Navigation
                                        <>
                                            <button
                                                onClick={() => setCurrentPage('admin-dashboard')}
                                                className={`px-3 py-2 rounded transition-colors ${
                                                    currentPage === 'admin-dashboard' ? 'bg-red-700' : 'hover:bg-red-700'
                                                } bg-red-600`}
                                            >
                                                Dashboard
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage('admin-requests')}
                                                className={`px-3 py-2 rounded transition-colors ${
                                                    currentPage === 'admin-requests' ? 'bg-red-700' : 'hover:bg-red-700'
                                                } bg-red-600`}
                                            >
                                                Requests
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage('admin-cards')}
                                                className={`px-3 py-2 rounded transition-colors ${
                                                    currentPage === 'admin-cards' ? 'bg-red-700' : 'hover:bg-red-700'
                                                } bg-red-600`}
                                            >
                                                Cards
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage('admin-users')}
                                                className={`px-3 py-2 rounded transition-colors ${
                                                    currentPage === 'admin-users' ? 'bg-red-700' : 'hover:bg-red-700'
                                                } bg-red-600`}
                                            >
                                                Users
                                            </button>
                                        </>
                                    ) : (
                                        // Student Navigation
                                        <>
                                            <button
                                                onClick={() => setCurrentPage('dashboard')}
                                                className={`px-3 py-2 rounded transition-colors ${
                                                    currentPage === 'dashboard' ? 'bg-blue-700' : 'hover:bg-blue-700'
                                                }`}
                                            >
                                                Dashboard
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage('practice')}
                                                className={`px-3 py-2 rounded transition-colors ${
                                                    currentPage === 'practice' ? 'bg-blue-700' : 'hover:bg-blue-700'
                                                }`}
                                            >
                                                Practice
                                            </button>
                                            <button
                                                onClick={() => setCurrentPage('request-word')}
                                                className={`px-3 py-2 rounded transition-colors ${
                                                    currentPage === 'request-word' ? 'bg-blue-700' : 'hover:bg-blue-700'
                                                }`}
                                            >
                                                Request Word
                                            </button>
                                        </>
                                    )}
                                    <button
                                        onClick={() => setCurrentPage('profile')}
                                        className={`px-3 py-2 rounded transition-colors ${
                                            currentPage === 'profile' ? 'bg-blue-700' : 'hover:bg-blue-700'
                                        }`}
                                    >
                                        Profile
                                    </button>
                                    <button
                                        onClick={handleLogout}
                                        className="px-3 py-2 rounded hover:bg-blue-700 transition-colors"
                                    >
                                        Logout
                                    </button>
                                </>
                            ) : (
                                <>
                                    <button
                                        onClick={() => setCurrentPage('login')}
                                        className="px-3 py-2 rounded hover:bg-blue-700 transition-colors"
                                    >
                                        Sign In
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage('register')}
                                        className="bg-blue-700 px-3 py-2 rounded hover:bg-blue-800 transition-colors"
                                    >
                                        Sign Up
                                    </button>
                                </>
                            )}
                        </div>
                    </div>
                </nav>
            );
        }
        
        // HomePage Component
        function HomePage() {
            const { user, setCurrentPage } = useContext(AppContext);

            if (user) {
                setCurrentPage('dashboard');
                return null;
            }

            return (
                <div className="max-w-4xl mx-auto text-center">
                    <div className="mb-12">
                        <h1 className="text-5xl font-bold text-gray-900 mb-6">
                            Learn Russian with <span className="text-blue-600">Smart Flashcards</span>
                        </h1>
                        <p className="text-xl text-gray-600 mb-8">
                            Master Russian vocabulary using scientifically-proven spaced repetition
                        </p>
                        <div className="space-x-4">
                            <button
                                onClick={() => setCurrentPage('register')}
                                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                            >
                                Get Started Free
                            </button>
                            <button
                                onClick={() => setCurrentPage('login')}
                                className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                            >
                                Sign In
                            </button>
                        </div>
                    </div>

                    <div className="grid md:grid-cols-3 gap-8 mb-12">
                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <div className="text-blue-600 text-3xl mb-4">
                                <i className="fas fa-brain"></i>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Spaced Repetition</h3>
                            <p className="text-gray-600">
                                Review words at optimal intervals to maximize retention and minimize study time.
                            </p>
                        </div>

                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <div className="text-blue-600 text-3xl mb-4">
                                <i className="fas fa-volume-up"></i>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Audio Pronunciation</h3>
                            <p className="text-gray-600">
                                Hear native Russian pronunciation for every word to improve your speaking skills.
                            </p>
                        </div>

                        <div className="bg-white p-6 rounded-lg shadow-md">
                            <div className="text-blue-600 text-3xl mb-4">
                                <i className="fas fa-search"></i>
                            </div>
                            <h3 className="text-xl font-semibold mb-2">Smart Search</h3>
                            <p className="text-gray-600">
                                Instantly find words in our database or create custom flashcards with ease.
                            </p>
                        </div>
                    </div>
                </div>
            );
        }

        // LoginPage Component
        function LoginPage() {
            const { setUser, setCurrentPage, api } = useContext(AppContext);
            const [formData, setFormData] = useState({ email: '', password: '' });
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');

                try {
                    const response = await api.post('/auth/login', formData);
                    localStorage.setItem('token', response.token);
                    setUser(response.user);
                    // Redirect admins to admin dashboard, regular users to user dashboard
                    setCurrentPage(response.user.isAdmin ? 'admin-dashboard' : 'dashboard');
                } catch (error) {
                    console.error('Login error:', error);
                    // Handle validation errors with detailed messages
                    if (error.errors && Array.isArray(error.errors)) {
                        const errorMessages = error.errors
                            .filter(err => err && err.msg) // Filter out null/undefined errors
                            .map(err => err.msg)
                            .join('. ');
                        setError(errorMessages || 'Validation failed');
                    } else {
                        setError(error.message || 'Login failed');
                    }
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
                    <h2 className="text-2xl font-bold text-center mb-6">Sign In</h2>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input
                                type="email"
                                value={formData.email}
                                onChange={(e) => setFormData({...formData, email: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input
                                type="password"
                                value={formData.password}
                                onChange={(e) => setFormData({...formData, password: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <button
                            onClick={handleSubmit}
                            disabled={loading}
                            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                        >
                            {loading ? 'Signing In...' : 'Sign In'}
                        </button>
                    </div>

                    <div className="text-center mt-4">
                        <span className="text-gray-600">Don't have an account? </span>
                        <button
                            onClick={() => setCurrentPage('register')}
                            className="text-blue-600 hover:underline"
                        >
                            Sign up
                        </button>
                    </div>
                </div>
            );
        }
        
        // RegisterPage Component
        function RegisterPage() {
            const { setUser, setCurrentPage, api } = useContext(AppContext);
            const [formData, setFormData] = useState({
                email: '',
                password: '',
                confirmPassword: '',
                firstName: '',
                lastName: ''
            });
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');

                if (formData.password !== formData.confirmPassword) {
                    setError('Passwords do not match');
                    setLoading(false);
                    return;
                }

                try {
                    const response = await api.post('/auth/register', {
                        email: formData.email,
                        password: formData.password,
                        firstName: formData.firstName,
                        lastName: formData.lastName
                    });
                    localStorage.setItem('token', response.token);
                    setUser(response.user);
                    // Redirect admins to admin dashboard, regular users to user dashboard
                    setCurrentPage(response.user.isAdmin ? 'admin-dashboard' : 'dashboard');
                } catch (error) {
                    console.error('Registration error:', error);
                    // Handle validation errors with detailed messages
                    if (error.errors && Array.isArray(error.errors)) {
                        const errorMessages = error.errors
                            .filter(err => err && err.msg) // Filter out null/undefined errors
                            .map(err => err.msg)
                            .join('. ');
                        setError(errorMessages || 'Validation failed');
                    } else {
                        setError(error.message || 'Registration failed');
                    }
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-8">
                    <h2 className="text-2xl font-bold text-center mb-6">Create Account</h2>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                <input
                                    type="text"
                                    value={formData.firstName}
                                    onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                <input
                                    type="text"
                                    value={formData.lastName}
                                    onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input
                                type="email"
                                value={formData.email}
                                onChange={(e) => setFormData({...formData, email: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input
                                type="password"
                                value={formData.password}
                                onChange={(e) => setFormData({...formData, password: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                            <input
                                type="password"
                                value={formData.confirmPassword}
                                onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        <button
                            onClick={handleSubmit}
                            disabled={loading}
                            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                        >
                            {loading ? 'Creating Account...' : 'Create Account'}
                        </button>
                    </div>

                    <div className="text-center mt-4">
                        <span className="text-gray-600">Already have an account? </span>
                        <button
                            onClick={() => setCurrentPage('login')}
                            className="text-blue-600 hover:underline"
                        >
                            Sign in
                        </button>
                    </div>
                </div>
            );
        }

        // Dashboard Component
        function Dashboard() {
            const { user, setCurrentPage, api } = useContext(AppContext);
            const [flashcards, setFlashcards] = useState([]);
            const [stats, setStats] = useState(null);
            const [loading, setLoading] = useState(true);
            const [searchTerm, setSearchTerm] = useState('');

            useEffect(() => {
                loadDashboardData();
            }, []);

            const loadDashboardData = async () => {
                try {
                    const [flashcardsResponse, statsResponse] = await Promise.all([
                        api.get('/flashcards?limit=10'),
                        api.get('/practice/stats')
                    ]);

                    setFlashcards(flashcardsResponse.flashcards || []);
                    setStats(statsResponse);
                } catch (error) {
                    console.error('Error loading dashboard data:', error);
                } finally {
                    setLoading(false);
                }
            };

            const startPractice = () => {
                setCurrentPage('practice');
            };

            if (loading) {
                return (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }

            return (
                <div className="max-w-6xl mx-auto">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">
                            Welcome back, {user?.firstName || 'Student'}!
                        </h1>
                        <p className="text-gray-600">
                            Ready to continue your Russian learning journey? Request new words or practice with admin-curated flashcards.
                        </p>
                    </div>

                    {/* Stats Cards */}
                    {stats && (
                        <div className="grid md:grid-cols-4 gap-6 mb-8">
                            <div className="bg-white p-6 rounded-lg shadow-md">
                                <div className="flex items-center">
                                    <div className="text-blue-600 text-2xl mr-3">
                                        <i className="fas fa-layer-group"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Cards</p>
                                        <p className="text-2xl font-bold">{stats.cards?.totalCards || 0}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white p-6 rounded-lg shadow-md">
                                <div className="flex items-center">
                                    <div className="text-orange-600 text-2xl mr-3">
                                        <i className="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Due for Review</p>
                                        <p className="text-2xl font-bold">{stats.cards?.dueForReview || 0}</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white p-6 rounded-lg shadow-md">
                                <div className="flex items-center">
                                    <div className="text-green-600 text-2xl mr-3">
                                        <i className="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Accuracy</p>
                                        <p className="text-2xl font-bold">{stats.sessions?.accuracy || 0}%</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-white p-6 rounded-lg shadow-md">
                                <div className="flex items-center">
                                    <div className="text-purple-600 text-2xl mr-3">
                                        <i className="fas fa-fire"></i>
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Study Streak</p>
                                        <p className="text-2xl font-bold">{stats.streak?.studyDays || 0} days</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex flex-wrap gap-4 mb-8">
                        <button
                            onClick={startPractice}
                            className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center"
                        >
                            <i className="fas fa-play mr-2"></i>
                            Start Practice Session
                        </button>

                        <button
                            onClick={() => setCurrentPage('request-word')}
                            className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center"
                        >
                            <i className="fas fa-plus mr-2"></i>
                            Request New Word
                        </button>
                    </div>

                    {/* Word Requests Status */}
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-xl font-semibold">Your Word Requests</h2>
                            <button
                                onClick={() => setCurrentPage('request-word')}
                                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                                View All →
                            </button>
                        </div>

                        <div className="text-center py-8">
                            <div className="text-blue-400 text-4xl mb-4">
                                <i className="fas fa-comment-dots"></i>
                            </div>
                            <h3 className="text-lg font-semibold mb-2">Request New Words</h3>
                            <p className="text-gray-600 mb-4">
                                Can't find a Russian word you want to learn? Request it and our admins will create a flashcard for you!
                            </p>
                            <button
                                onClick={() => setCurrentPage('request-word')}
                                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
                            >
                                Request Your First Word
                            </button>
                        </div>

                        {/* Quick Info */}
                        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="bg-gray-50 p-4 rounded-lg text-center">
                                <div className="text-2xl font-bold text-blue-600">Fast</div>
                                <div className="text-sm text-gray-600">Quick admin review</div>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg text-center">
                                <div className="text-2xl font-bold text-green-600">Quality</div>
                                <div className="text-sm text-gray-600">Professional flashcards</div>
                            </div>
                            <div className="bg-gray-50 p-4 rounded-lg text-center">
                                <div className="text-2xl font-bold text-purple-600">Audio</div>
                                <div className="text-sm text-gray-600">Native pronunciation</div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // CreateCardPage Component
        function CreateCardPage() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [formData, setFormData] = useState({
                russianWord: '',
                englishTranslations: [''],
                ipaTranscription: '',
                exampleSentence: '',
                imageUrl: '',
                difficultyLevel: 1,
                tags: []
            });
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');
            const [success, setSuccess] = useState('');
            const [searchResults, setSearchResults] = useState([]);
            const [searching, setSearching] = useState(false);

            const searchGlobalDatabase = async (word) => {
                if (!word || word.length < 2) {
                    setSearchResults([]);
                    return;
                }

                setSearching(true);
                try {
                    const response = await api.get(`/flashcards/search-global?q=${encodeURIComponent(word)}`);
                    setSearchResults(response.results || []);
                } catch (error) {
                    console.error('Search error:', error);
                    setSearchResults([]);
                } finally {
                    setSearching(false);
                }
            };

            const handleWordChange = (word) => {
                setFormData({...formData, russianWord: word});
                searchGlobalDatabase(word);
            };

            const selectFromGlobal = (globalCard) => {
                setFormData({
                    ...formData,
                    russianWord: globalCard.russian_word,
                    englishTranslations: globalCard.english_translations,
                    ipaTranscription: globalCard.ipa_transcription || '',
                    exampleSentence: globalCard.example_sentences?.[0] || ''
                });
                setSearchResults([]);
            };

            const addTranslation = () => {
                setFormData({
                    ...formData,
                    englishTranslations: [...formData.englishTranslations, '']
                });
            };

            const removeTranslation = (index) => {
                const newTranslations = formData.englishTranslations.filter((_, i) => i !== index);
                setFormData({...formData, englishTranslations: newTranslations});
            };

            const updateTranslation = (index, value) => {
                const newTranslations = [...formData.englishTranslations];
                newTranslations[index] = value;
                setFormData({...formData, englishTranslations: newTranslations});
            };

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');
                setSuccess('');

                // Filter out empty translations
                const validTranslations = formData.englishTranslations.filter(t => t.trim());

                if (validTranslations.length === 0) {
                    setError('At least one English translation is required');
                    setLoading(false);
                    return;
                }

                try {
                    await api.post('/flashcards', {
                        ...formData,
                        englishTranslations: validTranslations
                    });

                    setSuccess('Flashcard created successfully!');
                    setFormData({
                        russianWord: '',
                        englishTranslations: [''],
                        ipaTranscription: '',
                        exampleSentence: '',
                        imageUrl: '',
                        difficultyLevel: 1,
                        tags: []
                    });

                    setTimeout(() => {
                        setCurrentPage('dashboard');
                    }, 2000);

                } catch (error) {
                    console.error('Flashcard creation error:', error);
                    console.log('Error.errors:', error.errors); // Debug log
                    console.log('Error.errors type:', typeof error.errors, Array.isArray(error.errors)); // Debug log

                    // Handle validation errors with detailed messages
                    if (error.errors && Array.isArray(error.errors)) {
                        console.log('Processing validation errors:', error.errors); // Debug log
                        const errorMessages = error.errors
                            .filter(err => err && err.msg) // Filter out null/undefined errors
                            .map(err => {
                                console.log('Processing error:', err); // Debug each error
                                return err.msg;
                            })
                            .join('. ');
                        console.log('Final error message:', errorMessages); // Debug final message
                        setError(errorMessages || 'Validation failed');
                    } else {
                        setError(error.message || 'Failed to create flashcard');
                    }
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8">
                    <h2 className="text-2xl font-bold text-center mb-6">Create New Flashcard</h2>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    {success && (
                        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {success}
                        </div>
                    )}

                    <div className="space-y-6">
                        {/* Russian Word */}
                        <div className="relative">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Russian Word *
                            </label>
                            <input
                                type="text"
                                value={formData.russianWord}
                                onChange={(e) => handleWordChange(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="Enter Russian word..."
                                required
                            />

                            {/* Search Results */}
                            {searchResults.length > 0 && (
                                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                                    {searchResults.map((result, index) => (
                                        <button
                                            key={index}
                                            onClick={() => selectFromGlobal(result)}
                                            className="w-full text-left px-4 py-2 hover:bg-gray-100 border-b border-gray-200 last:border-b-0"
                                        >
                                            <div className="font-medium">{result.russian_word}</div>
                                            <div className="text-sm text-gray-600">
                                                {Array.isArray(result.english_translations)
                                                    ? result.english_translations.join(', ')
                                                    : result.english_translations}
                                            </div>
                                        </button>
                                    ))}
                                </div>
                            )}

                            {searching && (
                                <div className="absolute right-3 top-9">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                </div>
                            )}
                        </div>

                        {/* English Translations */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                English Translations *
                            </label>
                            {formData.englishTranslations.map((translation, index) => (
                                <div key={index} className="flex items-center space-x-2 mb-2">
                                    <input
                                        type="text"
                                        value={translation}
                                        onChange={(e) => updateTranslation(index, e.target.value)}
                                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="English translation..."
                                        required={index === 0}
                                    />
                                    {formData.englishTranslations.length > 1 && (
                                        <button
                                            onClick={() => removeTranslation(index)}
                                            className="text-red-600 hover:text-red-800"
                                        >
                                            <i className="fas fa-times"></i>
                                        </button>
                                    )}
                                </div>
                            ))}
                            <button
                                onClick={addTranslation}
                                className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                                + Add another translation
                            </button>
                        </div>

                        {/* IPA Transcription */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                IPA Transcription (optional)
                            </label>
                            <input
                                type="text"
                                value={formData.ipaTranscription}
                                onChange={(e) => setFormData({...formData, ipaTranscription: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="[pronunciation]"
                            />
                        </div>

                        {/* Example Sentence */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Example Sentence (optional)
                            </label>
                            <textarea
                                value={formData.exampleSentence}
                                onChange={(e) => setFormData({...formData, exampleSentence: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                rows="2"
                                maxLength="250"
                                placeholder="Example sentence in Russian..."
                            />
                            <div className="text-sm text-gray-500 mt-1">
                                {formData.exampleSentence.length}/250 characters
                            </div>
                        </div>

                        {/* Image URL */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Image URL (optional)
                            </label>
                            <input
                                type="url"
                                value={formData.imageUrl}
                                onChange={(e) => setFormData({...formData, imageUrl: e.target.value})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="https://example.com/image.jpg"
                            />
                            <div className="text-sm text-gray-500 mt-1">
                                Add an image to help remember this word
                            </div>
                        </div>

                        {/* Difficulty Level */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Difficulty Level
                            </label>
                            <select
                                value={formData.difficultyLevel}
                                onChange={(e) => setFormData({...formData, difficultyLevel: parseInt(e.target.value)})}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value={1}>1 - Very Easy</option>
                                <option value={2}>2 - Easy</option>
                                <option value={3}>3 - Medium</option>
                                <option value={4}>4 - Hard</option>
                                <option value={5}>5 - Very Hard</option>
                            </select>
                        </div>

                        {/* Submit Button */}
                        <div className="flex space-x-4">
                            <button
                                onClick={handleSubmit}
                                disabled={loading}
                                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                            >
                                {loading ? 'Creating...' : 'Create Flashcard'}
                            </button>

                            <button
                                onClick={() => setCurrentPage('dashboard')}
                                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            );
        }

        // PracticePage Component
        function PracticePage() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [session, setSession] = useState(null);
            const [currentCardIndex, setCurrentCardIndex] = useState(0);
            const [userAnswer, setUserAnswer] = useState('');
            const [showResult, setShowResult] = useState(false);
            const [isCorrect, setIsCorrect] = useState(false);
            const [sessionStats, setSessionStats] = useState({ correct: 0, incorrect: 0 });
            const [loading, setLoading] = useState(true);
            const [sessionComplete, setSessionComplete] = useState(false);
            const [incorrectCards, setIncorrectCards] = useState([]);

            useEffect(() => {
                loadPracticeSession();
            }, []);

            const loadPracticeSession = async () => {
                try {
                    const response = await api.get('/practice/session');
                    setSession(response);
                    setLoading(false);
                } catch (error) {
                    console.error('Error loading practice session:', error);
                    setLoading(false);
                }
            };

            const playAudio = (audioUrl) => {
                if (audioUrl) {
                    const audio = new Audio(audioUrl);
                    audio.play().catch(error => console.error('Audio play error:', error));
                }
            };

            const checkAnswer = () => {
                if (!session || !userAnswer.trim()) return;

                const currentCard = session.cards[currentCardIndex];
                const correctTranslations = currentCard.englishTranslations;
                const userAnswerLower = userAnswer.trim().toLowerCase();

                // Check if user answer matches any of the correct translations
                const correct = correctTranslations.some(translation =>
                    translation.toLowerCase() === userAnswerLower ||
                    translation.toLowerCase().includes(userAnswerLower) ||
                    userAnswerLower.includes(translation.toLowerCase())
                );

                setIsCorrect(correct);
                setShowResult(true);

                // Update session stats
                if (correct) {
                    setSessionStats(prev => ({ ...prev, correct: prev.correct + 1 }));
                } else {
                    setSessionStats(prev => ({ ...prev, incorrect: prev.incorrect + 1 }));
                    setIncorrectCards(prev => [...prev, currentCard]);
                }

                // Submit answer to backend
                submitAnswer(currentCard.cardId, correct);
            };

            const submitAnswer = async (cardId, correct) => {
                try {
                    await api.post('/practice/answer', {
                        cardId,
                        sessionId: session.sessionId,
                        userAnswer: userAnswer.trim(),
                        isCorrect: correct,
                        responseTime: 0 // Could implement timing later
                    });
                } catch (error) {
                    console.error('Error submitting answer:', error);
                }
            };

            const nextCard = () => {
                if (currentCardIndex < session.cards.length - 1) {
                    setCurrentCardIndex(currentCardIndex + 1);
                    setUserAnswer('');
                    setShowResult(false);
                } else {
                    // Session complete
                    setSessionComplete(true);
                }
            };

            const restartSession = () => {
                setCurrentCardIndex(0);
                setUserAnswer('');
                setShowResult(false);
                setSessionStats({ correct: 0, incorrect: 0 });
                setSessionComplete(false);
                setIncorrectCards([]);
                loadPracticeSession();
            };

            if (loading) {
                return (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }

            if (!session || session.cards.length === 0) {
                return (
                    <div className="max-w-2xl mx-auto text-center">
                        <div className="bg-white rounded-lg shadow-md p-8">
                            <div className="text-gray-400 text-4xl mb-4">
                                <i className="fas fa-layer-group"></i>
                            </div>
                            <h2 className="text-2xl font-bold mb-4">No Cards Available</h2>
                            <p className="text-gray-600 mb-6">
                                No flashcards are available for practice yet. Request some Russian words and our admins will create flashcards for you!
                            </p>
                            <button
                                onClick={() => setCurrentPage('request-word')}
                                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
                            >
                                Request Your First Word
                            </button>
                        </div>
                    </div>
                );
            }

            if (sessionComplete) {
                const accuracy = Math.round((sessionStats.correct / (sessionStats.correct + sessionStats.incorrect)) * 100);

                return (
                    <div className="max-w-2xl mx-auto">
                        <div className="bg-white rounded-lg shadow-md p-8 text-center">
                            <div className="text-green-600 text-4xl mb-4">
                                <i className="fas fa-check-circle"></i>
                            </div>
                            <h2 className="text-2xl font-bold mb-4">Session Complete!</h2>

                            <div className="grid grid-cols-3 gap-4 mb-6">
                                <div className="bg-green-100 p-4 rounded-lg">
                                    <div className="text-2xl font-bold text-green-600">{sessionStats.correct}</div>
                                    <div className="text-sm text-gray-600">Correct</div>
                                </div>
                                <div className="bg-red-100 p-4 rounded-lg">
                                    <div className="text-2xl font-bold text-red-600">{sessionStats.incorrect}</div>
                                    <div className="text-sm text-gray-600">Incorrect</div>
                                </div>
                                <div className="bg-blue-100 p-4 rounded-lg">
                                    <div className="text-2xl font-bold text-blue-600">{accuracy}%</div>
                                    <div className="text-sm text-gray-600">Accuracy</div>
                                </div>
                            </div>

                            {incorrectCards.length > 0 && (
                                <div className="mb-6">
                                    <h3 className="text-lg font-semibold mb-3">Review These Cards:</h3>
                                    <div className="space-y-2">
                                        {incorrectCards.map((card, index) => (
                                            <div key={index} className="bg-gray-100 p-3 rounded text-left">
                                                <div className="font-medium">{card.russianWord}</div>
                                                <div className="text-sm text-gray-600">
                                                    {Array.isArray(card.englishTranslations)
                                                        ? card.englishTranslations.join(', ')
                                                        : card.englishTranslations}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            <div className="space-x-4">
                                <button
                                    onClick={restartSession}
                                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    Practice Again
                                </button>
                                <button
                                    onClick={() => setCurrentPage('dashboard')}
                                    className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors"
                                >
                                    Back to Dashboard
                                </button>
                            </div>
                        </div>
                    </div>
                );
            }

            const currentCard = session.cards[currentCardIndex];
            const progress = ((currentCardIndex + 1) / session.cards.length) * 100;

            return (
                <div className="max-w-2xl mx-auto">
                    {/* Progress Bar */}
                    <div className="mb-6">
                        <div className="flex justify-between text-sm text-gray-600 mb-2">
                            <span>Card {currentCardIndex + 1} of {session.cards.length}</span>
                            <span>{sessionStats.correct} correct, {sessionStats.incorrect} incorrect</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${progress}%` }}
                            ></div>
                        </div>
                    </div>

                    {/* Practice Card */}
                    <div className="bg-white rounded-lg shadow-md p-8">
                        <div className="text-center mb-8">
                            <div className="flex justify-center items-center space-x-4 mb-4">
                                <h2 className="text-4xl font-bold text-gray-900">
                                    {currentCard.russianWord}
                                </h2>
                                {currentCard.audioUrl && (
                                    <button
                                        onClick={() => playAudio(currentCard.audioUrl)}
                                        className="text-blue-600 hover:text-blue-800 text-2xl"
                                    >
                                        <i className="fas fa-volume-up"></i>
                                    </button>
                                )}
                            </div>

                            {currentCard.ipaTranscription && (
                                <p className="text-lg text-gray-500 italic mb-4">
                                    [{currentCard.ipaTranscription}]
                                </p>
                            )}

                            {currentCard.exampleSentence && (
                                <p className="text-gray-600 italic mb-6">
                                    "{currentCard.exampleSentence}"
                                </p>
                            )}
                        </div>

                        {!showResult ? (
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Type the English translation:
                                    </label>
                                    <input
                                        type="text"
                                        value={userAnswer}
                                        onChange={(e) => setUserAnswer(e.target.value)}
                                        onKeyPress={(e) => e.key === 'Enter' && checkAnswer()}
                                        className="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="Your answer..."
                                        autoFocus
                                    />
                                </div>

                                <button
                                    onClick={checkAnswer}
                                    disabled={!userAnswer.trim()}
                                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                                >
                                    Check Answer
                                </button>
                            </div>
                        ) : (
                            <div className="space-y-4">
                                <div className={`p-4 rounded-lg ${isCorrect ? 'bg-green-100 border border-green-400' : 'bg-red-100 border border-red-400'}`}>
                                    <div className="flex items-center space-x-2 mb-2">
                                        <div className={`text-2xl ${isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                                            <i className={`fas ${isCorrect ? 'fa-star star-animation' : 'fa-times shake'}`}></i>
                                        </div>
                                        <span className={`font-semibold ${isCorrect ? 'text-green-800' : 'text-red-800'}`}>
                                            {isCorrect ? 'Correct!' : 'Incorrect'}
                                        </span>
                                    </div>

                                    <div className="text-sm">
                                        <p><strong>Your answer:</strong> {userAnswer}</p>
                                        <p><strong>Correct answers:</strong> {
                                            Array.isArray(currentCard.englishTranslations)
                                                ? currentCard.englishTranslations.join(', ')
                                                : currentCard.englishTranslations
                                        }</p>
                                    </div>
                                </div>

                                <button
                                    onClick={nextCard}
                                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors"
                                >
                                    {currentCardIndex < session.cards.length - 1 ? 'Next Card' : 'Finish Session'}
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            );
        }

        // ProfilePage Component
        function ProfilePage() {
            const { user, setUser, api } = useContext(AppContext);
            const [profile, setProfile] = useState(null);
            const [editing, setEditing] = useState(false);
            const [formData, setFormData] = useState({
                firstName: '',
                lastName: '',
                email: ''
            });
            const [passwordData, setPasswordData] = useState({
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
            });
            const [loading, setLoading] = useState(true);
            const [saving, setSaving] = useState(false);
            const [error, setError] = useState('');
            const [success, setSuccess] = useState('');
            const [showPasswordForm, setShowPasswordForm] = useState(false);

            useEffect(() => {
                loadProfile();
            }, []);

            const loadProfile = async () => {
                try {
                    const response = await api.get('/users/profile');
                    setProfile(response);
                    setFormData({
                        firstName: response.user.firstName || '',
                        lastName: response.user.lastName || '',
                        email: response.user.email || ''
                    });
                } catch (error) {
                    setError('Failed to load profile');
                } finally {
                    setLoading(false);
                }
            };

            const handleSaveProfile = async () => {
                setSaving(true);
                setError('');
                setSuccess('');

                try {
                    const response = await api.put('/users/profile', formData);
                    setProfile(prev => ({ ...prev, user: response.user }));
                    setUser(response.user);
                    setEditing(false);
                    setSuccess('Profile updated successfully!');
                } catch (error) {
                    setError(error.message || 'Failed to update profile');
                } finally {
                    setSaving(false);
                }
            };

            const handleChangePassword = async () => {
                if (passwordData.newPassword !== passwordData.confirmPassword) {
                    setError('New passwords do not match');
                    return;
                }

                setSaving(true);
                setError('');
                setSuccess('');

                try {
                    await api.put('/users/password', passwordData);
                    setPasswordData({
                        currentPassword: '',
                        newPassword: '',
                        confirmPassword: ''
                    });
                    setShowPasswordForm(false);
                    setSuccess('Password changed successfully!');
                } catch (error) {
                    setError(error.message || 'Failed to change password');
                } finally {
                    setSaving(false);
                }
            };

            if (loading) {
                return (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }

            return (
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-3xl font-bold text-gray-900 mb-8">Profile Settings</h1>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    {success && (
                        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {success}
                        </div>
                    )}

                    <div className="grid md:grid-cols-2 gap-8">
                        {/* Profile Information */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <div className="flex justify-between items-center mb-6">
                                <h2 className="text-xl font-semibold">Personal Information</h2>
                                {!editing && (
                                    <button
                                        onClick={() => setEditing(true)}
                                        className="text-blue-600 hover:text-blue-800"
                                    >
                                        <i className="fas fa-edit mr-1"></i>
                                        Edit
                                    </button>
                                )}
                            </div>

                            {editing ? (
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            First Name
                                        </label>
                                        <input
                                            type="text"
                                            value={formData.firstName}
                                            onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Last Name
                                        </label>
                                        <input
                                            type="text"
                                            value={formData.lastName}
                                            onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Email
                                        </label>
                                        <input
                                            type="email"
                                            value={formData.email}
                                            onChange={(e) => setFormData({...formData, email: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div className="flex space-x-3">
                                        <button
                                            onClick={handleSaveProfile}
                                            disabled={saving}
                                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                                        >
                                            {saving ? 'Saving...' : 'Save Changes'}
                                        </button>
                                        <button
                                            onClick={() => {
                                                setEditing(false);
                                                setFormData({
                                                    firstName: profile.user.firstName || '',
                                                    lastName: profile.user.lastName || '',
                                                    email: profile.user.email || ''
                                                });
                                            }}
                                            className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">First Name</label>
                                        <p className="text-gray-900">{profile?.user.firstName || 'Not set'}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Last Name</label>
                                        <p className="text-gray-900">{profile?.user.lastName || 'Not set'}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Email</label>
                                        <p className="text-gray-900">{profile?.user.email}</p>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Member Since</label>
                                        <p className="text-gray-900">
                                            {profile?.user.createdAt ? new Date(profile.user.createdAt).toLocaleDateString() : 'Unknown'}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Statistics */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h2 className="text-xl font-semibold mb-6">Learning Statistics</h2>

                            {profile?.statistics && (
                                <div className="space-y-4">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Total Flashcards</span>
                                        <span className="font-semibold">{profile.statistics.totalCards}</span>
                                    </div>

                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Cards Due for Review</span>
                                        <span className="font-semibold text-orange-600">{profile.statistics.dueCards}</span>
                                    </div>

                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Average Accuracy</span>
                                        <span className="font-semibold text-green-600">{profile.statistics.averageAccuracy}%</span>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Security Settings */}
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h2 className="text-xl font-semibold mb-6">Security</h2>

                            {!showPasswordForm ? (
                                <button
                                    onClick={() => setShowPasswordForm(true)}
                                    className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
                                >
                                    Change Password
                                </button>
                            ) : (
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Current Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.currentPassword}
                                            onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            New Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.newPassword}
                                            onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Confirm New Password
                                        </label>
                                        <input
                                            type="password"
                                            value={passwordData.confirmPassword}
                                            onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>

                                    <div className="flex space-x-3">
                                        <button
                                            onClick={handleChangePassword}
                                            disabled={saving}
                                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                                        >
                                            {saving ? 'Changing...' : 'Change Password'}
                                        </button>
                                        <button
                                            onClick={() => {
                                                setShowPasswordForm(false);
                                                setPasswordData({
                                                    currentPassword: '',
                                                    newPassword: '',
                                                    confirmPassword: ''
                                                });
                                            }}
                                            className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        }

        // WordRequestPage Component
        function WordRequestPage() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [formData, setFormData] = useState({
                russianWord: '',
                comments: ''
            });
            const [requests, setRequests] = useState([]);
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');
            const [success, setSuccess] = useState('');

            useEffect(() => {
                loadRequests();
            }, []);

            const loadRequests = async () => {
                try {
                    const data = await api.get('/requests');
                    setRequests(data.requests || []);
                } catch (error) {
                    console.error('Error loading requests:', error);
                }
            };

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');
                setSuccess('');

                try {
                    await api.post('/requests', formData);
                    setSuccess('Word request submitted successfully!');
                    setFormData({ russianWord: '', comments: '' });
                    loadRequests(); // Reload requests
                } catch (error) {
                    console.error('Request submission error:', error);
                    if (error.errors && Array.isArray(error.errors)) {
                        const errorMessages = error.errors
                            .filter(err => err && err.msg)
                            .map(err => err.msg)
                            .join('. ');
                        setError(errorMessages || 'Validation failed');
                    } else {
                        setError(error.message || 'Failed to submit request');
                    }
                } finally {
                    setLoading(false);
                }
            };

            const getStatusColor = (status) => {
                switch (status) {
                    case 'pending': return 'text-yellow-600 bg-yellow-100';
                    case 'approved': return 'text-green-600 bg-green-100';
                    case 'rejected': return 'text-red-600 bg-red-100';
                    default: return 'text-gray-600 bg-gray-100';
                }
            };

            return (
                <div className="max-w-4xl mx-auto">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-2">Request New Words</h1>
                        <p className="text-gray-600">
                            Request Russian words to be added to the flashcard database.
                            Admins will review and create flashcards for approved words.
                        </p>
                    </div>

                    {/* Request Form */}
                    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 className="text-xl font-semibold mb-4">Submit Word Request</h2>

                        {error && (
                            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                                {error}
                            </div>
                        )}

                        {success && (
                            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                                {success}
                            </div>
                        )}

                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Russian Word *
                                </label>
                                <input
                                    type="text"
                                    value={formData.russianWord}
                                    onChange={(e) => setFormData({...formData, russianWord: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="собака, дом, красивый..."
                                    required
                                />
                                <div className="text-sm text-gray-500 mt-1">
                                    Enter the Russian word you'd like to learn
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Comments (optional)
                                </label>
                                <textarea
                                    value={formData.comments}
                                    onChange={(e) => setFormData({...formData, comments: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    rows="3"
                                    maxLength="500"
                                    placeholder="Any additional context or notes about this word..."
                                />
                                <div className="text-sm text-gray-500 mt-1">
                                    {formData.comments.length}/500 characters
                                </div>
                            </div>

                            <button
                                onClick={handleSubmit}
                                disabled={loading || !formData.russianWord.trim()}
                                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                            >
                                {loading ? 'Submitting...' : 'Submit Request'}
                            </button>
                        </div>
                    </div>

                    {/* Request History */}
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-semibold mb-4">Your Requests</h2>

                        {requests.length > 0 ? (
                            <div className="space-y-4">
                                {requests.map(request => (
                                    <div key={request.request_id} className="border border-gray-200 rounded-lg p-4">
                                        <div className="flex justify-between items-start">
                                            <div className="flex-1">
                                                <div className="flex items-center mb-2">
                                                    <h3 className="text-lg font-medium text-gray-900 mr-3">
                                                        {request.russian_word}
                                                    </h3>
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                                                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                                                    </span>
                                                </div>
                                                {request.comments && (
                                                    <p className="text-gray-600 mb-2">{request.comments}</p>
                                                )}
                                                {request.admin_notes && (
                                                    <div className="bg-gray-50 p-3 rounded-md">
                                                        <p className="text-sm font-medium text-gray-700">Admin Notes:</p>
                                                        <p className="text-sm text-gray-600">{request.admin_notes}</p>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="text-sm text-gray-500 ml-4">
                                                {new Date(request.submitted_at).toLocaleDateString()}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <p className="text-gray-500">No requests yet. Submit your first word request above!</p>
                        )}
                    </div>
                </div>
            );
        }

        // AdminDashboard Component
        function AdminDashboard() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [dashboardData, setDashboardData] = useState(null);
            const [loading, setLoading] = useState(true);
            const [error, setError] = useState('');

            useEffect(() => {
                loadDashboard();
            }, []);

            const loadDashboard = async () => {
                try {
                    const data = await api.get('/admin/dashboard');
                    setDashboardData(data);
                } catch (error) {
                    setError('Failed to load dashboard data');
                    console.error('Dashboard error:', error);
                } finally {
                    setLoading(false);
                }
            };

            if (loading) {
                return (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                );
            }

            if (error) {
                return (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        {error}
                    </div>
                );
            }

            return (
                <div className="space-y-6">
                    <div className="flex justify-between items-center">
                        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                        <button
                            onClick={() => setCurrentPage('dashboard')}
                            className="text-blue-600 hover:text-blue-800"
                        >
                            ← Back to User Dashboard
                        </button>
                    </div>

                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                            <i className="fas fa-clock text-white"></i>
                                        </div>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Pending Requests
                                            </dt>
                                            <dd className="text-lg font-medium text-gray-900">
                                                {dashboardData?.pendingRequests || 0}
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                            <i className="fas fa-users text-white"></i>
                                        </div>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Total Users
                                            </dt>
                                            <dd className="text-lg font-medium text-gray-900">
                                                {dashboardData?.totalUsers || 0}
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                            <i className="fas fa-layer-group text-white"></i>
                                        </div>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Total Cards
                                            </dt>
                                            <dd className="text-lg font-medium text-gray-900">
                                                {dashboardData?.totalCards || 0}
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                            <i className="fas fa-list text-white"></i>
                                        </div>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Practice Sets
                                            </dt>
                                            <dd className="text-lg font-medium text-gray-900">
                                                {dashboardData?.totalSets || 0}
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Quick Actions
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <button
                                    onClick={() => setCurrentPage('admin-requests')}
                                    className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 hover:bg-yellow-100 transition-colors"
                                >
                                    <div className="flex items-center">
                                        <i className="fas fa-clock text-yellow-600 mr-3"></i>
                                        <div className="text-left">
                                            <div className="font-medium text-gray-900">Review Requests</div>
                                            <div className="text-sm text-gray-500">
                                                {dashboardData?.pendingRequests || 0} pending
                                            </div>
                                        </div>
                                    </div>
                                </button>

                                <button
                                    onClick={() => setCurrentPage('admin-cards')}
                                    className="bg-green-50 border border-green-200 rounded-lg p-4 hover:bg-green-100 transition-colors"
                                >
                                    <div className="flex items-center">
                                        <i className="fas fa-layer-group text-green-600 mr-3"></i>
                                        <div className="text-left">
                                            <div className="font-medium text-gray-900">Manage Cards</div>
                                            <div className="text-sm text-gray-500">Create and edit flashcards</div>
                                        </div>
                                    </div>
                                </button>

                                <button
                                    onClick={() => setCurrentPage('admin-users')}
                                    className="bg-blue-50 border border-blue-200 rounded-lg p-4 hover:bg-blue-100 transition-colors"
                                >
                                    <div className="flex items-center">
                                        <i className="fas fa-users text-blue-600 mr-3"></i>
                                        <div className="text-left">
                                            <div className="font-medium text-gray-900">Manage Users</div>
                                            <div className="text-sm text-gray-500">User administration</div>
                                        </div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Recent Activity */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Recent Activity
                            </h3>
                            {dashboardData?.recentActivity?.length > 0 ? (
                                <div className="space-y-3">
                                    {dashboardData.recentActivity.map((activity, index) => (
                                        <div key={index} className="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
                                            <div className="flex items-center">
                                                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                                <div>
                                                    <span className="font-medium">{activity.userEmail}</span>
                                                    <span className="text-gray-600"> requested word: </span>
                                                    <span className="font-medium">{activity.word}</span>
                                                </div>
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {new Date(activity.timestamp).toLocaleDateString()}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-500">No recent activity</p>
                            )}
                        </div>
                    </div>
                </div>
            );
        }

        // AdminCardsPage Component - Matches your existing interface
        function AdminCardsPage() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [formData, setFormData] = useState({
                russianWord: '',
                ipaTranscription: '',
                englishTranslations: [''],
                tags: [],
                partOfSpeech: 'noun',
                numberType: 'singular',
                gender: 'masculine',
                flashcardSets: [],
                imageFile: null,
                imageUrl: '',
                authorName: '',
                authorLink: '',
                useGoogleTts: true
            });
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');
            const [success, setSuccess] = useState('');
            const [isEditing, setIsEditing] = useState(false);
            const [editingCardId, setEditingCardId] = useState(null);

            // Predefined options
            const flashcardSets = [
                "100 Most Frequent Russian Adjectives",
                "100 Most Frequent Russian Nouns",
                "100 Most Frequent Russian Verbs",
                "300 Frequent Russian Words",
                "Advanced Names for Colors",
                "Advanced Verbs for Motion",
                "Age-related Vocabulary - 2",
                "Antonymic Adverbs",
                "Art Supplies",
                "Art Vocabulary",
                "Beginning",
                "Booze Related Words in Russian",
                "Business Vocabulary",
                "Chores",
                "Clothes",
                "Clothes Items",
                "Common Irregular Comparatives",
                "Comparatives",
                "Cooking Vocabulary",
                "Cookware",
                "Coronavirus Epidemic Vocabulary",
                "Covid Symptoms",
                "Crime and Punishment - Medicine Criminal Drugs",
                "Animals",
                "Body Parts",
                "Colors",
                "Days of the Week",
                "Emotions",
                "Family Members",
                "Food and Drinks",
                "Furniture",
                "Geography",
                "Health and Medicine",
                "Hobbies",
                "House and Home",
                "Jobs and Professions",
                "Money and Banking",
                "Months of the Year",
                "Music",
                "Nature",
                "Numbers",
                "School and Education",
                "Shopping",
                "Sports",
                "Technology",
                "Time",
                "Transportation",
                "Travel",
                "Weather",
                "Prepositions",
                "Conjunctions",
                "Pronouns",
                "Question Words",
                "Reflexive Verbs",
                "Irregular Verbs",
                "Perfective Verbs",
                "Imperfective Verbs",
                "Beginner Vocabulary",
                "Intermediate Vocabulary",
                "Advanced Vocabulary",
                "Expert Level Words"
            ];

            const partsOfSpeech = ["noun", "verb", "adjective", "adverb", "pronoun", "preposition", "conjunction", "interjection", "numeral", "particle"];
            const numberTypes = ["singular", "plural", "both", "uncountable"];
            const genders = ["masculine", "feminine", "neuter", "common"];

            const addTranslation = () => {
                setFormData({
                    ...formData,
                    englishTranslations: [...formData.englishTranslations, '']
                });
            };

            const removeTranslation = (index) => {
                const newTranslations = formData.englishTranslations.filter((_, i) => i !== index);
                setFormData({...formData, englishTranslations: newTranslations});
            };

            const updateTranslation = (index, value) => {
                const newTranslations = [...formData.englishTranslations];
                newTranslations[index] = value;
                setFormData({...formData, englishTranslations: newTranslations});
            };

            const toggleFlashcardSet = (setName) => {
                const currentSets = formData.flashcardSets;
                const newSets = currentSets.includes(setName)
                    ? currentSets.filter(s => s !== setName)
                    : [...currentSets, setName];
                setFormData({...formData, flashcardSets: newSets});
            };

            const handleImageUpload = (e) => {
                const file = e.target.files[0];
                if (file) {
                    setFormData({...formData, imageFile: file});
                }
            };

            const handleSubmit = async (e) => {
                e.preventDefault();
                setLoading(true);
                setError('');
                setSuccess('');

                // Filter out empty translations
                const validTranslations = formData.englishTranslations.filter(t => t.trim());

                if (validTranslations.length === 0) {
                    setError('At least one English translation is required');
                    setLoading(false);
                    return;
                }

                try {
                    const cardData = {
                        russianWord: formData.russianWord,
                        englishTranslations: validTranslations,
                        ipaTranscription: formData.ipaTranscription,
                        tags: formData.tags,
                        partOfSpeech: formData.partOfSpeech,
                        numberType: formData.numberType,
                        gender: formData.gender,
                        flashcardSets: formData.flashcardSets,
                        imageUrl: formData.imageUrl,
                        authorName: formData.authorName,
                        authorLink: formData.authorLink,
                        useGoogleTts: formData.useGoogleTts
                    };

                    if (isEditing) {
                        await api.put(`/admin/cards/${editingCardId}`, cardData);
                        setSuccess('Flashcard updated successfully!');
                    } else {
                        await api.post('/admin/cards', cardData);
                        setSuccess('Flashcard created successfully!');
                    }

                    // Reset form
                    setFormData({
                        russianWord: '',
                        ipaTranscription: '',
                        englishTranslations: [''],
                        tags: [],
                        partOfSpeech: 'noun',
                        numberType: 'singular',
                        gender: 'masculine',
                        flashcardSets: [],
                        imageFile: null,
                        imageUrl: '',
                        authorName: '',
                        authorLink: '',
                        useGoogleTts: true
                    });
                    setIsEditing(false);
                    setEditingCardId(null);

                } catch (error) {
                    console.error('Flashcard creation error:', error);
                    if (error.errors && Array.isArray(error.errors)) {
                        const errorMessages = error.errors
                            .filter(err => err && err.msg)
                            .map(err => err.msg)
                            .join('. ');
                        setError(errorMessages || 'Validation failed');
                    } else {
                        setError(error.message || 'Failed to save flashcard');
                    }
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="max-w-7xl mx-auto">
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <h1 className="text-3xl font-bold text-gray-900">
                                {isEditing ? 'Edit Flashcard' : 'Create New Flashcard'}
                            </h1>
                            <button
                                onClick={() => setCurrentPage('admin-dashboard')}
                                className="text-blue-600 hover:text-blue-800"
                            >
                                ← Back to Admin Dashboard
                            </button>
                        </div>
                    </div>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    {success && (
                        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                            {success}
                        </div>
                    )}

                    <div className="bg-white rounded-lg shadow-md p-8">
                        <form onSubmit={handleSubmit}>
                            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                                {/* Left Column */}
                                <div className="space-y-6">
                                    {/* Word */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Word *
                                        </label>
                                        <input
                                            type="text"
                                            value={formData.russianWord}
                                            onChange={(e) => setFormData({...formData, russianWord: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Enter Russian word..."
                                            required
                                        />
                                    </div>

                                    {/* Phonetic transcription (IPA) */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Phonetic transcription (IPA) *
                                        </label>
                                        <input
                                            type="text"
                                            value={formData.ipaTranscription}
                                            onChange={(e) => setFormData({...formData, ipaTranscription: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="[pronunciation]"
                                            required
                                        />
                                    </div>

                                    {/* English translation */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            English translation *
                                        </label>
                                        {formData.englishTranslations.map((translation, index) => (
                                            <div key={index} className="flex items-center space-x-2 mb-2">
                                                <input
                                                    type="text"
                                                    value={translation}
                                                    onChange={(e) => updateTranslation(index, e.target.value)}
                                                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                    placeholder="English translation..."
                                                    required={index === 0}
                                                />
                                                {formData.englishTranslations.length > 1 && (
                                                    <button
                                                        type="button"
                                                        onClick={() => removeTranslation(index)}
                                                        className="text-red-600 hover:text-red-800"
                                                    >
                                                        <i className="fas fa-times"></i>
                                                    </button>
                                                )}
                                            </div>
                                        ))}
                                        <button
                                            type="button"
                                            onClick={addTranslation}
                                            className="text-blue-600 hover:text-blue-800 text-sm"
                                        >
                                            + Add another translation
                                        </button>
                                    </div>

                                    {/* Tags */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Tags
                                        </label>
                                        <div className="flex space-x-2">
                                            <button
                                                type="button"
                                                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                                            >
                                                Vocabulary
                                            </button>
                                            <button
                                                type="button"
                                                className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                                            >
                                                Context Reverso
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                {/* Middle Column */}
                                <div className="space-y-6">
                                    {/* Part of speech */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Part of speech *
                                        </label>
                                        <select
                                            value={formData.partOfSpeech}
                                            onChange={(e) => setFormData({...formData, partOfSpeech: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                            {partsOfSpeech.map(pos => (
                                                <option key={pos} value={pos}>
                                                    {pos.charAt(0).toUpperCase() + pos.slice(1)}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Number */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Number
                                        </label>
                                        <select
                                            value={formData.numberType}
                                            onChange={(e) => setFormData({...formData, numberType: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                            {numberTypes.map(type => (
                                                <option key={type} value={type}>
                                                    {type.charAt(0).toUpperCase() + type.slice(1)}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Gender */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Gender
                                        </label>
                                        <select
                                            value={formData.gender}
                                            onChange={(e) => setFormData({...formData, gender: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                            {genders.map(gender => (
                                                <option key={gender} value={gender}>
                                                    {gender.charAt(0).toUpperCase() + gender.slice(1)}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    {/* Flashcard Sets */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Flashcard Sets
                                        </label>
                                        <div className="border border-gray-300 rounded-md max-h-64 overflow-y-auto p-2">
                                            {flashcardSets.map(setName => (
                                                <label key={setName} className="flex items-center space-x-2 py-1 hover:bg-gray-50">
                                                    <input
                                                        type="checkbox"
                                                        checked={formData.flashcardSets.includes(setName)}
                                                        onChange={() => toggleFlashcardSet(setName)}
                                                        className="rounded"
                                                    />
                                                    <span className="text-sm">{setName}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                </div>

                                {/* Right Column */}
                                <div className="space-y-6">
                                    {/* Image */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Image
                                        </label>
                                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                            <input
                                                type="file"
                                                accept="image/*"
                                                onChange={handleImageUpload}
                                                className="hidden"
                                                id="image-upload"
                                            />
                                            <label htmlFor="image-upload" className="cursor-pointer">
                                                <div className="text-gray-400 text-4xl mb-2">
                                                    <i className="fas fa-image"></i>
                                                </div>
                                                <div className="text-gray-600">
                                                    <span className="text-blue-600 hover:text-blue-800">Choose File</span>
                                                    <span className="ml-2">No file chosen</span>
                                                </div>
                                            </label>
                                        </div>

                                        {/* Image URL alternative */}
                                        <div className="mt-2">
                                            <input
                                                type="url"
                                                value={formData.imageUrl}
                                                onChange={(e) => setFormData({...formData, imageUrl: e.target.value})}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Or enter image URL..."
                                            />
                                        </div>
                                    </div>

                                    {/* Author name */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Author name
                                        </label>
                                        <input
                                            type="text"
                                            value={formData.authorName}
                                            onChange={(e) => setFormData({...formData, authorName: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="Author name..."
                                        />
                                    </div>

                                    {/* Author link */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Author link
                                        </label>
                                        <input
                                            type="url"
                                            value={formData.authorLink}
                                            onChange={(e) => setFormData({...formData, authorLink: e.target.value})}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            placeholder="https://..."
                                        />
                                    </div>

                                    {/* Google TTS Option */}
                                    <div>
                                        <label className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                checked={formData.useGoogleTts}
                                                onChange={(e) => setFormData({...formData, useGoogleTts: e.target.checked})}
                                                className="rounded"
                                            />
                                            <span className="text-sm font-medium text-gray-700">
                                                Generate audio with Google TTS
                                            </span>
                                        </label>
                                    </div>

                                    {/* Delete button (only when editing) */}
                                    {isEditing && (
                                        <button
                                            type="button"
                                            className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
                                        >
                                            Delete
                                        </button>
                                    )}
                                </div>
                            </div>

                            {/* Submit Button */}
                            <div className="mt-8 flex justify-center">
                                <button
                                    type="submit"
                                    disabled={loading}
                                    className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors font-semibold"
                                >
                                    {loading ? 'Creating...' : (isEditing ? 'Update Flashcard' : 'Create Flashcard')}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            );
        }

        // AdminRequestsPage Component
        function AdminRequestsPage() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [requests, setRequests] = useState([]);
            const [loading, setLoading] = useState(true);
            const [error, setError] = useState('');
            const [filter, setFilter] = useState('all');

            useEffect(() => {
                loadRequests();
            }, [filter]);

            const loadRequests = async () => {
                try {
                    setLoading(true);
                    const data = await api.get(`/admin/requests?status=${filter}`);
                    setRequests(data.requests || []);
                } catch (error) {
                    setError('Failed to load requests');
                    console.error('Requests error:', error);
                } finally {
                    setLoading(false);
                }
            };

            const handleApprove = async (requestId) => {
                try {
                    await api.post(`/admin/requests/${requestId}/approve`);
                    loadRequests(); // Reload requests
                } catch (error) {
                    console.error('Approve error:', error);
                    alert('Failed to approve request');
                }
            };

            const handleReject = async (requestId) => {
                const reason = prompt('Reason for rejection (optional):');
                try {
                    await api.post(`/admin/requests/${requestId}/reject`, {
                        adminNotes: reason
                    });
                    loadRequests(); // Reload requests
                } catch (error) {
                    console.error('Reject error:', error);
                    alert('Failed to reject request');
                }
            };

            const getStatusColor = (status) => {
                switch (status) {
                    case 'pending': return 'text-yellow-600 bg-yellow-100';
                    case 'approved': return 'text-green-600 bg-green-100';
                    case 'rejected': return 'text-red-600 bg-red-100';
                    default: return 'text-gray-600 bg-gray-100';
                }
            };

            return (
                <div className="max-w-6xl mx-auto">
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <h1 className="text-3xl font-bold text-gray-900">Word Requests</h1>
                            <button
                                onClick={() => setCurrentPage('admin-dashboard')}
                                className="text-blue-600 hover:text-blue-800"
                            >
                                ← Back to Admin Dashboard
                            </button>
                        </div>
                    </div>

                    {/* Filter Tabs */}
                    <div className="mb-6">
                        <div className="border-b border-gray-200">
                            <nav className="-mb-px flex space-x-8">
                                {['all', 'pending', 'approved', 'rejected'].map(status => (
                                    <button
                                        key={status}
                                        onClick={() => setFilter(status)}
                                        className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                            filter === status
                                                ? 'border-blue-500 text-blue-600'
                                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                    >
                                        {status.charAt(0).toUpperCase() + status.slice(1)}
                                    </button>
                                ))}
                            </nav>
                        </div>
                    </div>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    {loading ? (
                        <div className="flex justify-center items-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                        </div>
                    ) : (
                        <div className="bg-white shadow rounded-lg">
                            {requests.length > 0 ? (
                                <div className="divide-y divide-gray-200">
                                    {requests.map(request => (
                                        <div key={request.request_id} className="p-6">
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center mb-2">
                                                        <h3 className="text-lg font-medium text-gray-900 mr-3">
                                                            {request.russian_word}
                                                        </h3>
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                                                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                                                        </span>
                                                    </div>
                                                    <p className="text-sm text-gray-600 mb-1">
                                                        Requested by: <span className="font-medium">{request.user_email}</span>
                                                    </p>
                                                    <p className="text-sm text-gray-500 mb-2">
                                                        {new Date(request.submitted_at).toLocaleDateString()}
                                                    </p>
                                                    {request.comments && (
                                                        <p className="text-gray-600 mb-2">
                                                            <span className="font-medium">Comments:</span> {request.comments}
                                                        </p>
                                                    )}
                                                    {request.admin_notes && (
                                                        <div className="bg-gray-50 p-3 rounded-md">
                                                            <p className="text-sm font-medium text-gray-700">Admin Notes:</p>
                                                            <p className="text-sm text-gray-600">{request.admin_notes}</p>
                                                        </div>
                                                    )}
                                                </div>
                                                {request.status === 'pending' && (
                                                    <div className="flex space-x-2 ml-4">
                                                        <button
                                                            onClick={() => handleApprove(request.request_id)}
                                                            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                                                        >
                                                            Approve
                                                        </button>
                                                        <button
                                                            onClick={() => handleReject(request.request_id)}
                                                            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
                                                        >
                                                            Reject
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <div className="text-gray-400 text-4xl mb-4">
                                        <i className="fas fa-inbox"></i>
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No requests found</h3>
                                    <p className="text-gray-500">
                                        {filter === 'all' ? 'No word requests yet.' : `No ${filter} requests.`}
                                    </p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            );
        }

        // AdminUsersPage Component
        function AdminUsersPage() {
            const { api, setCurrentPage } = useContext(AppContext);
            const [users, setUsers] = useState([]);
            const [loading, setLoading] = useState(true);
            const [error, setError] = useState('');

            useEffect(() => {
                loadUsers();
            }, []);

            const loadUsers = async () => {
                try {
                    setLoading(true);
                    const data = await api.get('/admin/users');
                    setUsers(data.users || []);
                } catch (error) {
                    setError('Failed to load users');
                    console.error('Users error:', error);
                } finally {
                    setLoading(false);
                }
            };

            const toggleUserStatus = async (userId, isActive) => {
                try {
                    await api.put(`/admin/users/${userId}`, { isActive: !isActive });
                    loadUsers(); // Reload users
                } catch (error) {
                    console.error('Toggle user status error:', error);
                    alert('Failed to update user status');
                }
            };

            const toggleAdminStatus = async (userId, isAdmin) => {
                try {
                    await api.put(`/admin/users/${userId}`, { isAdmin: !isAdmin });
                    loadUsers(); // Reload users
                } catch (error) {
                    console.error('Toggle admin status error:', error);
                    alert('Failed to update admin status');
                }
            };

            return (
                <div className="max-w-6xl mx-auto">
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
                            <button
                                onClick={() => setCurrentPage('admin-dashboard')}
                                className="text-blue-600 hover:text-blue-800"
                            >
                                ← Back to Admin Dashboard
                            </button>
                        </div>
                    </div>

                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {error}
                        </div>
                    )}

                    {loading ? (
                        <div className="flex justify-center items-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                        </div>
                    ) : (
                        <div className="bg-white shadow rounded-lg overflow-hidden">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            User
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Activity
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Joined
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {users.map(user => (
                                        <tr key={user.user_id}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10">
                                                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                            <span className="text-sm font-medium text-gray-700">
                                                                {user.first_name?.charAt(0) || user.email.charAt(0).toUpperCase()}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {user.first_name} {user.last_name}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {user.email}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex flex-col space-y-1">
                                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                        user.is_active
                                                            ? 'bg-green-100 text-green-800'
                                                            : 'bg-red-100 text-red-800'
                                                    }`}>
                                                        {user.is_active ? 'Active' : 'Inactive'}
                                                    </span>
                                                    {user.is_admin && (
                                                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                                            Admin
                                                        </span>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <div>
                                                    <div>{user.request_count || 0} requests</div>
                                                    <div>{user.practice_count || 0} sessions</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {new Date(user.created_at).toLocaleDateString()}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div className="flex space-x-2">
                                                    <button
                                                        onClick={() => toggleUserStatus(user.user_id, user.is_active)}
                                                        className={`px-3 py-1 rounded text-xs ${
                                                            user.is_active
                                                                ? 'bg-red-100 text-red-800 hover:bg-red-200'
                                                                : 'bg-green-100 text-green-800 hover:bg-green-200'
                                                        }`}
                                                    >
                                                        {user.is_active ? 'Deactivate' : 'Activate'}
                                                    </button>
                                                    <button
                                                        onClick={() => toggleAdminStatus(user.user_id, user.is_admin)}
                                                        className={`px-3 py-1 rounded text-xs ${
                                                            user.is_admin
                                                                ? 'bg-purple-100 text-purple-800 hover:bg-purple-200'
                                                                : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                                                        }`}
                                                    >
                                                        {user.is_admin ? 'Remove Admin' : 'Make Admin'}
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            );
        }

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
