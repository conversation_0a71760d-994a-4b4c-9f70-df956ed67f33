version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: russian-flashcards-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: russian_flashcards
      POSTGRES_USER: flashcards_user
      POSTGRES_PASSWORD: flashcards_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./scripts/seed-data.sql:/docker-entrypoint-initdb.d/02-seed-data.sql
    ports:
      - "5432:5432"
    networks:
      - flashcards-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U flashcards_user -d russian_flashcards"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache (optional, for future use)
  redis:
    image: redis:7-alpine
    container_name: russian-flashcards-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - flashcards-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Russian Flashcards Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: russian-flashcards-app
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      
      # Database Configuration
      DB_HOST: database
      DB_PORT: 5432
      DB_NAME: russian_flashcards
      DB_USER: flashcards_user
      DB_PASSWORD: flashcards_password
      DB_SSL: false
      
      # JWT Configuration
      JWT_SECRET: your_super_secret_jwt_key_change_this_in_production
      JWT_EXPIRES_IN: 7d
      
      # Redis Configuration (optional)
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # API Keys (set these in .env file)
      GOOGLE_TTS_API_KEY: ${GOOGLE_TTS_API_KEY:-}
      FORVO_API_KEY: ${FORVO_API_KEY:-}
      YANDEX_DICTIONARY_API_KEY: ${YANDEX_DICTIONARY_API_KEY:-}
      
      # Email Configuration (optional)
      EMAIL_HOST: ${EMAIL_HOST:-}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USER: ${EMAIL_USER:-}
      EMAIL_PASSWORD: ${EMAIL_PASSWORD:-}
      EMAIL_FROM: ${EMAIL_FROM:-<EMAIL>}
      
      # File Upload Configuration
      MAX_FILE_SIZE: 5242880
      UPLOAD_PATH: ./uploads
      
      # Rate Limiting
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
      
    ports:
      - "3000:3000"
    volumes:
      - ./uploads:/app/uploads
      - app_logs:/app/logs
    networks:
      - flashcards-network
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: russian-flashcards-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - flashcards-network
    depends_on:
      - app
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  flashcards-network:
    driver: bridge
