# Russian Flashcards - Podman Containerfile
FROM docker.io/node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies including development tools
RUN apk add --no-cache \
    postgresql-client \
    curl \
    git \
    bash \
    vim \
    nano

# Install nodemon globally for development
RUN npm install -g nodemon

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Create uploads directory
RUN mkdir -p uploads/audio

# Set proper permissions
RUN chown -R node:node /app

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application in development mode
CMD ["npm", "run", "dev"]
