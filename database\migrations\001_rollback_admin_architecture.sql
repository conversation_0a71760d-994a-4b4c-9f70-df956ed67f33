-- Rollback 001: Remove Admin Architecture
-- This script rolls back the admin-centric architecture changes

-- Step 1: Drop triggers
DROP TRIGGER IF EXISTS update_user_card_overlays_updated_at ON user_card_overlays;
DROP TRIGGER IF EXISTS update_practice_sets_updated_at ON practice_sets;

-- Step 2: Drop indexes
DROP INDEX IF EXISTS idx_users_is_admin;
DROP INDEX IF EXISTS idx_flashcards_created_by;
DROP INDEX IF EXISTS idx_word_requests_status;
DROP INDEX IF EXISTS idx_word_requests_user_id;
DROP INDEX IF EXISTS idx_overlays_user_card;
DROP INDEX IF EXISTS idx_practice_sets_active;
DROP INDEX IF EXISTS idx_practice_sets_created_by;

-- Step 3: Drop new tables
DROP TABLE IF EXISTS practice_sets;
DROP TABLE IF EXISTS user_card_overlays;
DROP TABLE IF EXISTS word_requests;

-- Step 4: Remove new columns
ALTER TABLE flashcards DROP COLUMN IF EXISTS created_by;
ALTER TABLE users DROP COLUMN IF EXISTS is_admin;

-- <PERSON><PERSON> completed
SELECT 'Rollback 001 completed successfully' as status;
