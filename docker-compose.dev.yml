version: '3.8'

services:
  # PostgreSQL Database for Development
  database:
    image: postgres:15-alpine
    container_name: russian-flashcards-db-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: russian_flashcards_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    networks:
      - flashcards-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev_user -d russian_flashcards_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Development
  redis:
    image: redis:7-alpine
    container_name: russian-flashcards-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_dev_data:/data
    networks:
      - flashcards-dev-network

  # Development Application with Hot Reload
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: russian-flashcards-app-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      
      # Database Configuration
      DB_HOST: database
      DB_PORT: 5432
      DB_NAME: russian_flashcards_dev
      DB_USER: dev_user
      DB_PASSWORD: dev_password
      DB_SSL: false
      
      # JWT Configuration
      JWT_SECRET: dev_jwt_secret_key_not_for_production
      JWT_EXPIRES_IN: 24h
      
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
      # Development API Keys (use your own)
      GOOGLE_TTS_API_KEY: ${GOOGLE_TTS_API_KEY:-}
      FORVO_API_KEY: ${FORVO_API_KEY:-}
      
      # Development settings
      DEBUG: true
      LOG_LEVEL: debug
      
    ports:
      - "3001:3000"  # Different port for development
    volumes:
      - .:/app
      - /app/node_modules  # Prevent overwriting node_modules
      - ./uploads:/app/uploads
    networks:
      - flashcards-dev-network
    depends_on:
      database:
        condition: service_healthy
    command: npm run dev

  # pgAdmin for Database Management (Development only)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: russian-flashcards-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - flashcards-dev-network
    depends_on:
      - database

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  flashcards-dev-network:
    driver: bridge
