# Russian Flashcards - Spaced Repetition Learning Platform

A comprehensive web-based platform for learning Russian vocabulary using scientifically-proven spaced repetition algorithms. Features include user authentication, flashcard management, audio pronunciation, and intelligent practice sessions.

## Features

### 🧠 Spaced Repetition Algorithm
- **SuperMemo SM-2 based algorithm** for optimal review scheduling
- **Adaptive difficulty** based on user performance
- **Intelligent card prioritization** for maximum learning efficiency

### 🔊 Audio Pronunciation
- **Google Text-to-Speech integration** for native Russian pronunciation
- **Forvo API fallback** for additional pronunciation sources
- **Auto-generated audio** for all Russian words

### 🔍 Smart Search & Database
- **Global flashcard database** with pre-populated common Russian words
- **Instant word lookup** with auto-completion
- **IPA transcription** and example sentences
- **Frequency-based word ranking**

### 👤 User Management
- **Secure authentication** with JWT tokens
- **Password hashing** using bcrypt
- **Profile management** and statistics tracking
- **Session history** and progress analytics

### 📱 Responsive Design
- **Mobile-friendly interface** built with Tailwind CSS
- **Progressive Web App** ready for mobile installation
- **Intuitive user experience** with smooth animations

## Technology Stack

### Backend
- **Node.js** with Express.js framework
- **PostgreSQL** database with optimized indexing
- **JWT authentication** with secure session management
- **Rate limiting** and security middleware
- **RESTful API** design

### Frontend
- **React 18** with functional components and hooks
- **Tailwind CSS** for responsive styling
- **Font Awesome** icons
- **Single-page application** architecture

### External APIs
- **Google Text-to-Speech API** for audio generation
- **Forvo API** for pronunciation fallback
- **Yandex Dictionary API** for word definitions (optional)

## Installation & Setup

### Prerequisites
- Node.js 16+ and npm
- PostgreSQL 12+
- Google Cloud account (for TTS API)
- Forvo API account (optional)

### 1. Clone and Install Dependencies
```bash
git clone <repository-url>
cd russian-flashcards
npm install
```

### 2. Database Setup
```bash
# Create PostgreSQL database
createdb russian_flashcards

# Run schema
psql -d russian_flashcards -f database/schema.sql
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env
```

Required environment variables:
```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=russian_flashcards
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# JWT Secret
JWT_SECRET=your_super_secret_jwt_key_here

# Google TTS API
GOOGLE_TTS_API_KEY=your_google_tts_api_key

# Optional: Forvo API
FORVO_API_KEY=your_forvo_api_key
```

### 4. Start the Application
```bash
# Development mode
npm run dev

# Production mode
npm start
```

The application will be available at `http://localhost:3000`

## API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/verify` - Token verification
- `POST /api/auth/logout` - User logout

### Flashcard Endpoints
- `GET /api/flashcards` - Get user's flashcards (paginated)
- `POST /api/flashcards` - Create new flashcard
- `GET /api/flashcards/:id` - Get specific flashcard
- `PUT /api/flashcards/:id` - Update flashcard
- `DELETE /api/flashcards/:id` - Delete flashcard
- `GET /api/flashcards/search-global` - Search global database

### Practice Endpoints
- `GET /api/practice/session` - Get practice session (12 cards)
- `POST /api/practice/answer` - Submit answer for review
- `GET /api/practice/stats` - Get practice statistics

### User Endpoints
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `PUT /api/users/password` - Change password

## Database Schema

### Core Tables
- **users** - User accounts and authentication
- **flashcards** - User-created flashcards
- **practice_sessions** - Spaced repetition tracking
- **session_history** - Practice session records
- **global_flashcards** - Shared word database

### Key Features
- **Full-text search** indexes for Russian words
- **Automatic timestamps** with triggers
- **Foreign key constraints** for data integrity
- **Optimized indexes** for performance

## Spaced Repetition Algorithm

The platform uses a modified SuperMemo SM-2 algorithm:

1. **Initial intervals**: 1 day, then 6 days for first correct answers
2. **Ease factor**: Starts at 2.5, adjusts based on performance
3. **Interval calculation**: Previous interval × ease factor
4. **Error handling**: Reset to 1 day for incorrect answers
5. **Priority scoring**: Overdue cards get higher priority

## Deployment

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Configure production database
- [ ] Set secure JWT secret
- [ ] Enable HTTPS
- [ ] Configure rate limiting
- [ ] Set up monitoring

### Deployment Platforms
- **Heroku**: Easy deployment with PostgreSQL addon
- **Vercel**: Frontend deployment with serverless functions
- **AWS**: Full control with EC2 and RDS
- **DigitalOcean**: Cost-effective VPS hosting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review the API endpoints

## Roadmap

### Planned Features
- [ ] Mobile app (React Native)
- [ ] Offline mode support
- [ ] Social features (sharing cards)
- [ ] Advanced analytics
- [ ] Multiple language support
- [ ] Gamification elements
- [ ] Export/import functionality

### Performance Optimizations
- [ ] Redis caching
- [ ] CDN for audio files
- [ ] Database query optimization
- [ ] Frontend code splitting
