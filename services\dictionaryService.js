const axios = require('axios');
const { query } = require('../config/database');

/**
 * Dictionary service for looking up Russian words and auto-filling flashcard data
 */

/**
 * Search for a word in the global flashcards database
 * @param {string} word - Russian word to search for
 * @returns {Object|null} Word data if found
 */
async function searchGlobalWord(word) {
    try {
        const result = await query(
            `SELECT 
                global_card_id,
                russian_word,
                english_translations,
                ipa_transcription,
                audio_url,
                example_sentences,
                frequency_rank,
                word_type
            FROM global_flashcards 
            WHERE LOWER(russian_word) = LOWER($1)
            LIMIT 1`,
            [word.trim()]
        );

        if (result.rows.length > 0) {
            const wordData = result.rows[0];
            
            // Increment usage count
            await query(
                'UPDATE global_flashcards SET usage_count = usage_count + 1 WHERE global_card_id = $1',
                [wordData.global_card_id]
            );

            return {
                russianWord: wordData.russian_word,
                englishTranslations: wordData.english_translations,
                ipaTranscription: wordData.ipa_transcription,
                audioUrl: wordData.audio_url,
                exampleSentences: wordData.example_sentences,
                frequencyRank: wordData.frequency_rank,
                wordType: wordData.word_type,
                source: 'global_database'
            };
        }

        return null;

    } catch (error) {
        console.error('Global word search error:', error);
        return null;
    }
}

/**
 * Look up word using Yandex Dictionary API
 * @param {string} word - Russian word to look up
 * @returns {Object|null} Word data if found
 */
async function lookupWithYandex(word) {
    const apiKey = process.env.YANDEX_DICTIONARY_API_KEY;
    
    if (!apiKey) {
        console.warn('Yandex Dictionary API key not configured');
        return null;
    }

    try {
        const response = await axios.get('https://dictionary.yandex.net/api/v1/dicservice.json/lookup', {
            params: {
                key: apiKey,
                lang: 'ru-en',
                text: word.trim(),
                ui: 'en'
            }
        });

        const definitions = response.data.def;
        
        if (!definitions || definitions.length === 0) {
            return null;
        }

        const firstDef = definitions[0];
        const translations = [];
        const examples = [];

        // Extract translations
        if (firstDef.tr) {
            firstDef.tr.forEach(translation => {
                if (translation.text) {
                    translations.push(translation.text);
                }
                
                // Extract examples
                if (translation.ex) {
                    translation.ex.forEach(example => {
                        if (example.text && example.tr && example.tr[0] && example.tr[0].text) {
                            examples.push(`${example.text} - ${example.tr[0].text}`);
                        }
                    });
                }
            });
        }

        return {
            russianWord: word.trim(),
            englishTranslations: translations,
            ipaTranscription: firstDef.ts || null, // Transcription if available
            exampleSentences: examples.slice(0, 3), // Limit to 3 examples
            wordType: firstDef.pos || null, // Part of speech
            source: 'yandex_dictionary'
        };

    } catch (error) {
        console.error('Yandex Dictionary API error:', error.response?.data || error.message);
        return null;
    }
}

/**
 * Look up word using multiple sources
 * @param {string} word - Russian word to look up
 * @returns {Object|null} Combined word data from available sources
 */
async function lookupWord(word) {
    if (!word || typeof word !== 'string') {
        return null;
    }

    const cleanWord = word.trim();
    
    if (cleanWord.length === 0) {
        return null;
    }

    // First, try global database
    let wordData = await searchGlobalWord(cleanWord);
    
    if (wordData) {
        return wordData;
    }

    // If not found in global database, try external APIs
    wordData = await lookupWithYandex(cleanWord);
    
    if (wordData) {
        // Save to global database for future use
        try {
            await query(
                `INSERT INTO global_flashcards 
                (russian_word, english_translations, ipa_transcription, example_sentences, word_type, usage_count)
                VALUES ($1, $2, $3, $4, $5, 1)
                ON CONFLICT (russian_word) DO UPDATE SET
                    usage_count = global_flashcards.usage_count + 1`,
                [
                    wordData.russianWord,
                    JSON.stringify(wordData.englishTranslations),
                    wordData.ipaTranscription,
                    JSON.stringify(wordData.exampleSentences || []),
                    wordData.wordType
                ]
            );
        } catch (error) {
            console.error('Error saving word to global database:', error);
        }
    }

    return wordData;
}

/**
 * Get word suggestions based on partial input
 * @param {string} partial - Partial Russian word
 * @param {number} limit - Maximum number of suggestions
 * @returns {Array} Array of word suggestions
 */
async function getWordSuggestions(partial, limit = 10) {
    try {
        if (!partial || partial.length < 2) {
            return [];
        }

        const result = await query(
            `SELECT 
                russian_word,
                english_translations,
                frequency_rank,
                usage_count
            FROM global_flashcards 
            WHERE russian_word ILIKE $1
            ORDER BY 
                frequency_rank ASC NULLS LAST,
                usage_count DESC,
                LENGTH(russian_word) ASC
            LIMIT $2`,
            [`${partial}%`, limit]
        );

        return result.rows.map(row => ({
            word: row.russian_word,
            translations: row.english_translations,
            frequencyRank: row.frequency_rank,
            usageCount: row.usage_count
        }));

    } catch (error) {
        console.error('Word suggestions error:', error);
        return [];
    }
}

/**
 * Get popular words for study recommendations
 * @param {number} limit - Maximum number of words to return
 * @returns {Array} Array of popular words
 */
async function getPopularWords(limit = 50) {
    try {
        const result = await query(
            `SELECT 
                russian_word,
                english_translations,
                ipa_transcription,
                example_sentences,
                frequency_rank,
                word_type,
                usage_count
            FROM global_flashcards 
            WHERE frequency_rank IS NOT NULL
            ORDER BY frequency_rank ASC
            LIMIT $1`,
            [limit]
        );

        return result.rows.map(row => ({
            russianWord: row.russian_word,
            englishTranslations: row.english_translations,
            ipaTranscription: row.ipa_transcription,
            exampleSentences: row.example_sentences,
            frequencyRank: row.frequency_rank,
            wordType: row.word_type,
            usageCount: row.usage_count
        }));

    } catch (error) {
        console.error('Popular words error:', error);
        return [];
    }
}

/**
 * Add a new word to the global database
 * @param {Object} wordData - Word data to add
 * @returns {boolean} Success status
 */
async function addToGlobalDatabase(wordData) {
    try {
        const {
            russianWord,
            englishTranslations,
            ipaTranscription,
            exampleSentences,
            wordType,
            frequencyRank
        } = wordData;

        await query(
            `INSERT INTO global_flashcards 
            (russian_word, english_translations, ipa_transcription, example_sentences, word_type, frequency_rank, usage_count)
            VALUES ($1, $2, $3, $4, $5, $6, 1)
            ON CONFLICT (russian_word) DO UPDATE SET
                english_translations = EXCLUDED.english_translations,
                ipa_transcription = COALESCE(EXCLUDED.ipa_transcription, global_flashcards.ipa_transcription),
                example_sentences = EXCLUDED.example_sentences,
                word_type = COALESCE(EXCLUDED.word_type, global_flashcards.word_type),
                frequency_rank = COALESCE(EXCLUDED.frequency_rank, global_flashcards.frequency_rank),
                usage_count = global_flashcards.usage_count + 1,
                updated_at = CURRENT_TIMESTAMP`,
            [
                russianWord,
                JSON.stringify(englishTranslations),
                ipaTranscription,
                JSON.stringify(exampleSentences || []),
                wordType,
                frequencyRank
            ]
        );

        return true;

    } catch (error) {
        console.error('Error adding word to global database:', error);
        return false;
    }
}

module.exports = {
    searchGlobalWord,
    lookupWithYandex,
    lookupWord,
    getWordSuggestions,
    getPopularWords,
    addToGlobalDatabase
};
