const express = require('express');
const { body, validationResult } = require('express-validator');
const { query, transaction } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');
const { calculateNextReview, updateSpacedRepetition } = require('../services/spacedRepetition');

const router = express.Router();

// Get practice session (12 cards due for review)
router.get('/session', authenticateToken, async (req, res) => {
    try {
        // Get cards due for review, prioritizing overdue cards
        const result = await query(
            `SELECT 
                f.card_id,
                f.russian_word,
                f.english_translations,
                f.ipa_transcription,
                f.audio_url,
                f.example_sentence,
                f.image_url,
                f.difficulty_level,
                ps.last_reviewed,
                ps.next_review,
                ps.correct_count,
                ps.incorrect_count,
                ps.ease_factor,
                ps.interval_days,
                ps.repetition_number
            FROM flashcards f
            JOIN practice_sessions ps ON f.card_id = ps.card_id
            WHERE f.user_id = $1 
            AND ps.next_review <= CURRENT_TIMESTAMP
            ORDER BY 
                ps.next_review ASC,
                ps.last_reviewed ASC NULLS FIRST,
                RANDOM()
            LIMIT 12`,
            [req.userId]
        );

        // If we don't have enough due cards, fill with random cards
        if (result.rows.length < 12) {
            const additionalCards = await query(
                `SELECT 
                    f.card_id,
                    f.russian_word,
                    f.english_translations,
                    f.ipa_transcription,
                    f.audio_url,
                    f.example_sentence,
                    f.image_url,
                    f.difficulty_level,
                    ps.last_reviewed,
                    ps.next_review,
                    ps.correct_count,
                    ps.incorrect_count,
                    ps.ease_factor,
                    ps.interval_days,
                    ps.repetition_number
                FROM flashcards f
                JOIN practice_sessions ps ON f.card_id = ps.card_id
                WHERE f.user_id = $1 
                AND ps.next_review > CURRENT_TIMESTAMP
                AND f.card_id NOT IN (${result.rows.map((_, i) => `$${i + 2}`).join(',') || 'NULL'})
                ORDER BY RANDOM()
                LIMIT $${result.rows.length + 2}`,
                [req.userId, ...result.rows.map(row => row.card_id), 12 - result.rows.length]
            );

            result.rows.push(...additionalCards.rows);
        }

        // Shuffle the cards for the session
        const shuffledCards = result.rows.sort(() => Math.random() - 0.5);

        // Create session history entry
        const sessionResult = await query(
            `INSERT INTO session_history (user_id, cards_reviewed, session_type)
            VALUES ($1, $2, 'practice')
            RETURNING history_id, session_date`,
            [req.userId, shuffledCards.length]
        );

        res.json({
            sessionId: sessionResult.rows[0].history_id,
            sessionDate: sessionResult.rows[0].session_date,
            cards: shuffledCards.map(card => ({
                cardId: card.card_id,
                russianWord: card.russian_word,
                englishTranslations: card.english_translations,
                ipaTranscription: card.ipa_transcription,
                audioUrl: card.audio_url,
                exampleSentence: card.example_sentence,
                imageUrl: card.image_url,
                difficultyLevel: card.difficulty_level,
                practiceData: {
                    lastReviewed: card.last_reviewed,
                    nextReview: card.next_review,
                    correctCount: card.correct_count,
                    incorrectCount: card.incorrect_count,
                    easeFactor: card.ease_factor,
                    intervalDays: card.interval_days,
                    repetitionNumber: card.repetition_number
                }
            })),
            totalCards: shuffledCards.length
        });

    } catch (error) {
        console.error('Get practice session error:', error);
        res.status(500).json({
            message: 'Internal server error while creating practice session'
        });
    }
});

// Submit answer for a card
router.post('/answer', authenticateToken, [
    body('cardId').isInt().withMessage('Card ID must be an integer'),
    body('sessionId').isInt().withMessage('Session ID must be an integer'),
    body('userAnswer').trim().notEmpty().withMessage('User answer is required'),
    body('isCorrect').isBoolean().withMessage('isCorrect must be a boolean'),
    body('responseTime').optional().isInt({ min: 0 }).withMessage('Response time must be a positive integer')
], async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { cardId, sessionId, userAnswer, isCorrect, responseTime } = req.body;

        // Verify card belongs to user
        const cardResult = await query(
            'SELECT card_id FROM flashcards WHERE card_id = $1 AND user_id = $2',
            [cardId, req.userId]
        );

        if (cardResult.rows.length === 0) {
            return res.status(404).json({
                message: 'Card not found or does not belong to user'
            });
        }

        // Get current practice session data
        const practiceResult = await query(
            `SELECT 
                correct_count, 
                incorrect_count, 
                ease_factor, 
                interval_days, 
                repetition_number
            FROM practice_sessions 
            WHERE card_id = $1 AND user_id = $2`,
            [cardId, req.userId]
        );

        if (practiceResult.rows.length === 0) {
            return res.status(404).json({
                message: 'Practice session not found'
            });
        }

        const currentData = practiceResult.rows[0];

        // Calculate new spaced repetition values
        const newData = updateSpacedRepetition(currentData, isCorrect);

        // Update practice session
        await transaction(async (client) => {
            // Update practice session
            await client.query(
                `UPDATE practice_sessions 
                SET 
                    last_reviewed = CURRENT_TIMESTAMP,
                    next_review = $1,
                    correct_count = $2,
                    incorrect_count = $3,
                    ease_factor = $4,
                    interval_days = $5,
                    repetition_number = $6,
                    updated_at = CURRENT_TIMESTAMP
                WHERE card_id = $7 AND user_id = $8`,
                [
                    newData.nextReview,
                    newData.correctCount,
                    newData.incorrectCount,
                    newData.easeFactor,
                    newData.intervalDays,
                    newData.repetitionNumber,
                    cardId,
                    req.userId
                ]
            );

            // Update session history if session exists
            if (sessionId) {
                await client.query(
                    `UPDATE session_history 
                    SET 
                        cards_correct = cards_correct + $1,
                        cards_incorrect = cards_incorrect + $2
                    WHERE history_id = $3 AND user_id = $4`,
                    [isCorrect ? 1 : 0, isCorrect ? 0 : 1, sessionId, req.userId]
                );
            }
        });

        res.json({
            message: 'Answer submitted successfully',
            isCorrect,
            nextReview: newData.nextReview,
            intervalDays: newData.intervalDays,
            easeFactor: newData.easeFactor,
            streakData: {
                correctCount: newData.correctCount,
                incorrectCount: newData.incorrectCount,
                repetitionNumber: newData.repetitionNumber
            }
        });

    } catch (error) {
        console.error('Submit answer error:', error);
        res.status(500).json({
            message: 'Internal server error while submitting answer'
        });
    }
});

// Get practice statistics
router.get('/stats', authenticateToken, async (req, res) => {
    try {
        const timeframe = req.query.timeframe || '7'; // days
        const validTimeframes = ['1', '7', '30', '90', 'all'];
        const selectedTimeframe = validTimeframes.includes(timeframe) ? timeframe : '7';

        let dateCondition = '';
        if (selectedTimeframe !== 'all') {
            dateCondition = `AND session_date >= CURRENT_DATE - INTERVAL '${selectedTimeframe} days'`;
        }

        // Get session statistics
        const sessionStats = await query(
            `SELECT 
                COUNT(*) as total_sessions,
                SUM(cards_reviewed) as total_cards_reviewed,
                SUM(cards_correct) as total_correct,
                SUM(cards_incorrect) as total_incorrect,
                AVG(session_duration_minutes) as avg_session_duration
            FROM session_history 
            WHERE user_id = $1 ${dateCondition}`,
            [req.userId]
        );

        // Get cards due for review
        const dueCards = await query(
            `SELECT COUNT(*) as due_count
            FROM practice_sessions ps
            JOIN flashcards f ON ps.card_id = f.card_id
            WHERE f.user_id = $1 AND ps.next_review <= CURRENT_TIMESTAMP`,
            [req.userId]
        );

        // Get total cards
        const totalCards = await query(
            'SELECT COUNT(*) as total_count FROM flashcards WHERE user_id = $1',
            [req.userId]
        );

        // Get streak information
        const streakData = await query(
            `SELECT 
                COUNT(DISTINCT DATE(session_date)) as study_days,
                MAX(session_date) as last_study_date
            FROM session_history 
            WHERE user_id = $1 
            AND session_date >= CURRENT_DATE - INTERVAL '30 days'`,
            [req.userId]
        );

        const stats = sessionStats.rows[0];
        const accuracy = stats.total_cards_reviewed > 0 
            ? Math.round((stats.total_correct / stats.total_cards_reviewed) * 100) 
            : 0;

        res.json({
            timeframe: selectedTimeframe,
            sessions: {
                totalSessions: parseInt(stats.total_sessions) || 0,
                totalCardsReviewed: parseInt(stats.total_cards_reviewed) || 0,
                totalCorrect: parseInt(stats.total_correct) || 0,
                totalIncorrect: parseInt(stats.total_incorrect) || 0,
                accuracy: accuracy,
                avgSessionDuration: Math.round(parseFloat(stats.avg_session_duration) || 0)
            },
            cards: {
                totalCards: parseInt(totalCards.rows[0].total_count) || 0,
                dueForReview: parseInt(dueCards.rows[0].due_count) || 0
            },
            streak: {
                studyDays: parseInt(streakData.rows[0].study_days) || 0,
                lastStudyDate: streakData.rows[0].last_study_date
            }
        });

    } catch (error) {
        console.error('Get practice stats error:', error);
        res.status(500).json({
            message: 'Internal server error while fetching practice statistics'
        });
    }
});

module.exports = router;
