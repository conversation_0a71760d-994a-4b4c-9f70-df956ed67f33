# 🔧 Technical Specification - Admin-Centric Russian Flashcard Platform

## 📋 Task Reference Guide

This document provides detailed technical specifications for each task in the refactoring plan.

---

## 🗄️ Database Schema Specifications

### **New Tables to Create**

#### 1. Word Requests Table
```sql
CREATE TABLE word_requests (
    request_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    russian_word VARCHAR(100) NOT NULL,
    comments TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    admin_notes TEXT,
    processed_by UUID REFERENCES users(user_id),
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP
);

CREATE INDEX idx_word_requests_status ON word_requests(status);
CREATE INDEX idx_word_requests_user_id ON word_requests(user_id);
```

#### 2. User Card Overlays Table
```sql
CREATE TABLE user_card_overlays (
    overlay_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    card_id UUID NOT NULL REFERENCES flashcards(card_id) ON DELETE CASCADE,
    notes TEXT,
    user_examples TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, card_id)
);

CREATE INDEX idx_overlays_user_card ON user_card_overlays(user_id, card_id);
```

#### 3. Practice Sets Table
```sql
CREATE TABLE practice_sets (
    set_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    card_ids JSONB NOT NULL,
    criteria JSONB, -- {"tags": ["food", "accusative"], "difficulty": [1,2,3]}
    assigned_user_ids JSONB, -- null = all users, array = specific users
    created_by UUID NOT NULL REFERENCES users(user_id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_practice_sets_active ON practice_sets(is_active);
CREATE INDEX idx_practice_sets_created_by ON practice_sets(created_by);
```

### **Table Modifications**

#### 1. Users Table - Add Admin Role
```sql
ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT false;
CREATE INDEX idx_users_is_admin ON users(is_admin);
```

#### 2. Flashcards Table - Ensure Compliance
```sql
-- Ensure audio_url is required (if not already)
ALTER TABLE flashcards ALTER COLUMN audio_url SET NOT NULL;

-- Add created_by column to track admin who created the card
ALTER TABLE flashcards ADD COLUMN created_by UUID REFERENCES users(user_id);

-- Add tags column if not exists
ALTER TABLE flashcards ADD COLUMN tags JSONB DEFAULT '[]';
CREATE INDEX idx_flashcards_tags ON flashcards USING GIN(tags);
```

---

## 🔐 Authentication & Authorization

### **JWT Token Structure**
```javascript
// Updated JWT payload
{
  userId: "uuid",
  email: "<EMAIL>",
  isAdmin: boolean,
  iat: timestamp,
  exp: timestamp
}
```

### **Admin Middleware Implementation**
```javascript
// middleware/adminAuth.js
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }
  
  if (!req.user.isAdmin) {
    return res.status(403).json({ message: 'Admin access required' });
  }
  
  next();
};

// Usage: app.use('/api/admin', authenticateToken, requireAdmin);
```

---

## 🌐 API Endpoint Specifications

### **User Word Requests**

#### POST /api/requests
```javascript
// Request body
{
  "russianWord": "собака",
  "comments": "Need this word for animal vocabulary"
}

// Response
{
  "requestId": "uuid",
  "status": "pending",
  "submittedAt": "2024-01-01T00:00:00Z"
}
```

#### GET /api/requests
```javascript
// Response
{
  "requests": [
    {
      "requestId": "uuid",
      "russianWord": "собака",
      "status": "pending",
      "submittedAt": "2024-01-01T00:00:00Z",
      "comments": "Need this word for animal vocabulary"
    }
  ],
  "pagination": { ... }
}
```

### **Admin Endpoints**

#### GET /api/admin/dashboard
```javascript
// Response
{
  "pendingRequests": 15,
  "totalUsers": 150,
  "totalCards": 500,
  "totalSets": 25,
  "recentActivity": [
    {
      "type": "request_submitted",
      "user": "<EMAIL>",
      "word": "собака",
      "timestamp": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST /api/admin/requests/:id/approve
```javascript
// Request body
{
  "adminNotes": "Good word for beginner set"
}

// Response
{
  "message": "Request approved",
  "cardId": "uuid" // if card was created
}
```

#### POST /api/admin/sets
```javascript
// Request body
{
  "name": "Food Vocabulary - Beginner",
  "description": "Basic food words for new learners",
  "cardIds": ["uuid1", "uuid2", ...], // exactly 12 cards
  "criteria": {
    "tags": ["food"],
    "difficulty": [1, 2]
  },
  "assignedUserIds": null // null = all users, array = specific users
}
```

### **User Overlays**

#### POST /api/overlays
```javascript
// Request body
{
  "cardId": "uuid",
  "notes": "Remember: sounds like 'so-BAH-ka'",
  "userExamples": "Моя собака очень умная"
}

// Response
{
  "overlayId": "uuid",
  "message": "Overlay created successfully"
}
```

---

## 🎨 Frontend Component Structure

### **New Components to Create**

#### 1. WordRequestForm
```jsx
// components/WordRequestForm.jsx
function WordRequestForm() {
  const [formData, setFormData] = useState({
    russianWord: '',
    comments: ''
  });
  
  // Submit word request logic
  // Validation for Russian characters
  // Success/error handling
}
```

#### 2. AdminDashboard
```jsx
// components/admin/AdminDashboard.jsx
function AdminDashboard() {
  // Pending requests widget
  // User metrics
  // Recent activity feed
  // Quick actions
}
```

#### 3. PracticeSetSelector
```jsx
// components/PracticeSetSelector.jsx
function PracticeSetSelector() {
  // Display assigned sets
  // Set difficulty indicators
  // Progress tracking
  // Set selection logic
}
```

#### 4. OverlayEditor
```jsx
// components/OverlayEditor.jsx
function OverlayEditor({ cardId, existingOverlay }) {
  // Notes input
  // User examples input
  // Save/cancel actions
  // Real-time preview
}
```

### **Route Structure Updates**

#### User Routes
- `/` - Home/Login
- `/dashboard` - User dashboard with assigned sets and request status
- `/request-word` - Word request form
- `/practice/:setId` - Practice session with overlays
- `/my-requests` - Request history and status
- `/profile` - User profile

#### Admin Routes (Protected)
- `/admin` - Admin dashboard
- `/admin/requests` - Manage word requests
- `/admin/cards` - Card management
- `/admin/sets` - Practice set management
- `/admin/users` - User management
- `/admin/settings` - Platform settings

---

## 🔄 Data Migration Strategy

### **Migration Steps**

1. **Backup Current Data**
```sql
-- Create backup tables
CREATE TABLE flashcards_backup AS SELECT * FROM flashcards;
CREATE TABLE users_backup AS SELECT * FROM users;
CREATE TABLE practice_sessions_backup AS SELECT * FROM practice_sessions;
```

2. **Add New Columns**
```sql
-- Add admin role to existing users
ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT false;

-- Promote first user to admin for testing
UPDATE users SET is_admin = true WHERE user_id = (
  SELECT user_id FROM users ORDER BY created_at LIMIT 1
);
```

3. **Create New Tables**
```sql
-- Execute all new table creation scripts
-- Add indexes and constraints
```

4. **Migrate Existing Data**
```sql
-- Convert existing user cards to admin cards
UPDATE flashcards SET created_by = (
  SELECT user_id FROM users WHERE is_admin = true LIMIT 1
) WHERE created_by IS NULL;

-- Create default practice set from existing cards
INSERT INTO practice_sets (name, description, card_ids, assigned_user_ids, created_by)
SELECT 
  'Default Set',
  'Migrated from existing cards',
  json_agg(card_id),
  null,
  (SELECT user_id FROM users WHERE is_admin = true LIMIT 1)
FROM (
  SELECT card_id FROM flashcards ORDER BY created_at LIMIT 12
) subquery;
```

---

## 🧪 Testing Strategy

### **Unit Tests**
- API endpoint tests for all new routes
- Middleware tests for admin authentication
- Database model tests for new tables
- Frontend component tests

### **Integration Tests**
- Complete user workflow: request → approval → practice
- Complete admin workflow: review → create → assign
- Authentication and authorization flows
- Data migration scripts

### **Test Data**
```sql
-- Create test admin user
INSERT INTO users (email, password_hash, is_admin) VALUES 
('<EMAIL>', '$2b$10$...', true);

-- Create test word requests
INSERT INTO word_requests (user_id, russian_word, comments) VALUES
((SELECT user_id FROM users WHERE email = '<EMAIL>'), 'тест', 'Test word');

-- Create test practice set
INSERT INTO practice_sets (name, card_ids, created_by) VALUES
('Test Set', '["uuid1", "uuid2"]', (SELECT user_id FROM users WHERE is_admin = true));
```

---

## 📦 Deployment Considerations

### **Environment Variables**
```bash
# Add to .env
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure_password
ENABLE_ADMIN_SEEDING=true
```

### **Database Migrations**
- Create numbered migration files
- Include rollback scripts
- Test migrations on staging first
- Monitor performance during migration

### **Feature Flags**
- Gradual rollout of admin features
- Ability to toggle between old/new workflows
- A/B testing capabilities

---

*This technical specification provides implementation details for all tasks in the admin-centric refactoring plan.*
